# Jackson LocalDateTime 反序列化修复

## 问题描述

在处理RocketMQ消息时，遇到了Jackson反序列化错误：

```
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling
```

错误发生在`AliyunMessageListener.processResumeParseMessage`方法中，当尝试将包含`LocalDateTime`字段的JSON消息反序列化为`ResumeParseMessage`对象时。

## 根本原因

`AliyunMessageListener`类中创建了一个新的`ObjectMapper`实例，但没有注册`JavaTimeModule`来处理Java 8的时间类型：

```java
public AliyunMessageListener(ResumeParseMessageConsumer resumeParseConsumer) {
    this.resumeParseConsumer = resumeParseConsumer;
    this.objectMapper = new ObjectMapper(); // 缺少JavaTimeModule配置
}
```

虽然项目中已经有正确配置的`ObjectMapper`（在`CacheConfig`和`RedisConfig`中），但`AliyunMessageListener`没有使用它们。

## 解决方案

修改`AliyunMessageListener`的构造函数，通过Spring依赖注入使用已配置的`ObjectMapper`：

### 修改前
```java
public AliyunMessageListener(ResumeParseMessageConsumer resumeParseConsumer) {
    this.resumeParseConsumer = resumeParseConsumer;
    this.objectMapper = new ObjectMapper();
}
```

### 修改后
```java
public AliyunMessageListener(ResumeParseMessageConsumer resumeParseConsumer, ObjectMapper objectMapper) {
    this.resumeParseConsumer = resumeParseConsumer;
    this.objectMapper = objectMapper;
}
```

## 验证

创建了测试类`AliyunMessageListenerTest`来验证修复：

1. **testLocalDateTimeDeserialization**: 测试从JSON字符串反序列化包含LocalDateTime的消息
2. **testLocalDateTimeSerialization**: 测试将包含LocalDateTime的对象序列化为JSON

测试结果显示：
- LocalDateTime被序列化为数组格式：`[2025,7,2,15,35,13,433611979]`
- 反序列化能正确处理这种格式
- 所有字段都能正确映射

## 相关文件

- `src/main/java/com/tinyzk/user/center/consumer/AliyunMessageListener.java` - 主要修复文件
- `src/main/java/com/tinyzk/user/center/dto/ResumeParseMessage.java` - 包含LocalDateTime字段的DTO
- `src/main/java/com/tinyzk/user/center/config/CacheConfig.java` - 已配置JavaTimeModule的ObjectMapper
- `src/test/java/com/tinyzk/user/center/consumer/AliyunMessageListenerTest.java` - 验证测试

## 依赖确认

项目已包含必要的依赖：

```xml
<dependency>
    <groupId>com.fasterxml.jackson.datatype</groupId>
    <artifactId>jackson-datatype-jsr310</artifactId>
</dependency>
```

## 最佳实践

1. **避免创建新的ObjectMapper实例**：应该使用Spring管理的、已配置的ObjectMapper
2. **统一配置**：确保所有ObjectMapper实例都有相同的配置
3. **测试覆盖**：为序列化/反序列化逻辑编写测试用例

## 修复日期

2025-07-02

## 修复人员

AI Assistant (Claude)
