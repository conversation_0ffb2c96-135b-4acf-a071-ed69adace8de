# RocketMQ问题诊断和修复指南

基于阿里云RocketMQ最佳实践指南的完整解决方案

## 🔍 问题诊断结果

### 发现的主要问题

1. **NameServer地址配置错误** ❌
   - **问题**: `application-local.yml`中配置为`rocketmq-nameserver:9876`
   - **影响**: 应用无法连接到RocketMQ服务
   - **修复**: 已修改为`localhost:9876`

2. **消费者配置不完整** ❌
   - **问题**: 缺少完整的消费者Bean配置
   - **影响**: 消费者可能无法正确启动
   - **修复**: 添加了`RocketMQConsumerConfig`配置类

3. **Topic可能未创建** ❌
   - **问题**: 必需的Topic可能不存在
   - **影响**: 消息发送失败
   - **修复**: 提供了Topic创建脚本

4. **缺少监控和健康检查** ❌
   - **问题**: 无法及时发现连接问题
   - **修复**: 添加了健康检查和测试接口

## 🔧 修复方案实施

### 1. 配置文件修复

**文件**: `src/main/resources/application-local.yml`

```yaml
# RocketMQ本地配置
rocketmq:
  # 修复：使用localhost地址
  name-server: localhost:9876
  producer:
    group: user-center-producer-group
    instance-name: user-center-producer-instance
  consumer:
    group: user-center-consumer-group
```

### 2. 新增配置类

- ✅ `RocketMQConsumerConfig.java` - 消费者配置
- ✅ `RocketMQHealthIndicator.java` - 健康检查
- ✅ `RocketMQTopicService.java` - Topic管理
- ✅ `RocketMQTestController.java` - 测试接口

### 3. 消费者注解优化

```java
@RocketMQMessageListener(
    topic = "RESUME_PARSE_TOPIC",
    consumerGroup = "resume-parse-consumer-group",
    consumeMode = ConsumeMode.CONCURRENTLY, // 并发消费
    maxReconsumeTimes = 3,
    consumeThreadMax = 20,
    messageModel = MessageModel.CLUSTERING
)
```

## 🚀 验证步骤

### 步骤1: 启动RocketMQ服务

```bash
# 1. 启动RocketMQ Docker服务
cd docker/rocketmq
docker-compose up -d

# 2. 检查服务状态
docker ps | grep rocketmq

# 3. 查看日志
docker logs rocketmq-nameserver
docker logs rocketmq-broker
```

### 步骤2: 创建必需的Topic

```bash
# 执行Topic创建脚本
./scripts/create-topics.sh

# 或手动创建
docker exec -it rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t RESUME_PARSE_TOPIC -c DefaultCluster -q 4
```

### 步骤3: 启动应用程序

```bash
# 使用本地配置启动
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 或使用jar包启动
java -jar target/user-center-*.jar --spring.profiles.active=local
```

### 步骤4: 验证连接和功能

#### 4.1 健康检查
```bash
curl http://localhost:8080/actuator/health
```

#### 4.2 测试消息发送
```bash
# 测试简历解析消息
curl -X POST http://localhost:8080/api/test/rocketmq/test-resume-parse

# 测试通用消息
curl -X POST "http://localhost:8080/api/test/rocketmq/test-general-message?topic=TEST_TOPIC&tag=TEST"
```

#### 4.3 检查Topic状态
```bash
curl http://localhost:8080/api/test/rocketmq/topic-status
```

## 📊 监控和调试

### 1. RocketMQ Console
- 访问: http://localhost:8080 (RocketMQ Console)
- 检查Topic、消费者组、消息状态

### 2. 应用日志监控
```bash
# 查看RocketMQ相关日志
tail -f logs/application.log | grep -i rocketmq

# 查看消费者日志
tail -f logs/application.log | grep -i "ResumeParseMessageConsumer"
```

### 3. 指标监控
- 消息发送成功/失败计数
- 消息消费成功/失败计数
- 消费延迟监控

## 🔧 常见问题解决

### 问题1: 连接超时
**症状**: `ConnectException` 或 `TimeoutException`
**解决**:
```bash
# 检查端口是否开放
netstat -an | grep 9876
netstat -an | grep 10911

# 检查防火墙设置
sudo ufw status
```

### 问题2: Topic不存在
**症状**: `TopicNotExistException`
**解决**:
```bash
# 创建Topic
docker exec -it rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t YOUR_TOPIC -c DefaultCluster
```

### 问题3: 消费者无法启动
**症状**: 消费者组无法注册
**解决**:
1. 检查消费者组名称是否唯一
2. 确认Topic已创建
3. 检查网络连接

### 问题4: 消息堆积
**症状**: 消息发送成功但未被消费
**解决**:
1. 检查消费者是否正常运行
2. 增加消费者线程数
3. 检查消费逻辑是否有异常

## 📋 最佳实践检查清单

- ✅ NameServer地址配置正确
- ✅ Topic已创建并配置合适的队列数
- ✅ 消费者组配置正确
- ✅ 消息序列化/反序列化正常
- ✅ 错误处理和重试机制完善
- ✅ 监控和告警配置
- ✅ 性能参数调优

## 🎯 下一步优化建议

1. **性能优化**
   - 调整消费者线程数
   - 优化批量消费配置
   - 实施消息压缩

2. **可靠性增强**
   - 实施死信队列处理
   - 添加消息轨迹
   - 完善监控告警

3. **运维改进**
   - 自动化部署脚本
   - 性能监控仪表板
   - 故障自动恢复机制

## 📞 技术支持

如果问题仍然存在，请提供以下信息：
1. 应用启动日志
2. RocketMQ容器日志
3. 网络连接测试结果
4. 具体错误信息和堆栈跟踪
