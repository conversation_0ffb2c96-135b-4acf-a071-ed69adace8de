package com.tinyzk.user.center.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AliyunMessageListener测试类
 * 主要测试ObjectMapper对LocalDateTime的序列化和反序列化
 */
@SpringBootTest
public class AliyunMessageListenerTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testLocalDateTimeDeserialization() throws Exception {
        // 模拟从RocketMQ接收到的消息JSON（包含LocalDateTime）
        String messageJson = """
            {
                "messageId": "1751441713432-1210",
                "batchId": "batch_1751441710128_8be01348",
                "fileName": "51job_高昌鑫(90690455).doc",
                "fileType": "doc",
                "fileSize": 63218,
                "ossKey": "79407b668be48bfa3a275d73276d16c0.doc",
                "fileUrl": "https://tzk-resume.oss-cn-shanghai.aliyuncs.com/resume-files/2025/07/02/79407b668be48bfa3a275d73276d16c0.doc",
                "priority": 5,
                "parseParams": {
                    "rawtext": true,
                    "handleImage": true,
                    "avatar": true,
                    "parseMode": "fast",
                    "ocrMode": "accurate",
                    "ocrService": "OCR"
                },
                "createTime": "2025-07-02T15:35:13.433611979",
                "userId": null,
                "tenantId": null
            }
            """;

        // 测试反序列化
        ResumeParseMessage message = objectMapper.readValue(messageJson, ResumeParseMessage.class);

        // 验证基本字段
        assertNotNull(message);
        assertEquals("1751441713432-1210", message.getMessageId());
        assertEquals("batch_1751441710128_8be01348", message.getBatchId());
        assertEquals("51job_高昌鑫(90690455).doc", message.getFileName());
        assertEquals("doc", message.getFileType());
        assertEquals(Long.valueOf(63218), message.getFileSize());
        assertEquals(Integer.valueOf(5), message.getPriority());

        // 验证LocalDateTime字段
        assertNotNull(message.getCreateTime());
        assertEquals(2025, message.getCreateTime().getYear());
        assertEquals(7, message.getCreateTime().getMonthValue());
        assertEquals(2, message.getCreateTime().getDayOfMonth());
        assertEquals(15, message.getCreateTime().getHour());
        assertEquals(35, message.getCreateTime().getMinute());
        assertEquals(13, message.getCreateTime().getSecond());

        // 验证解析参数
        assertNotNull(message.getParseParams());
        assertTrue(message.getParseParams().getRawtext());
        assertTrue(message.getParseParams().getHandleImage());
        assertTrue(message.getParseParams().getAvatar());
        assertEquals("fast", message.getParseParams().getParseMode());
        assertEquals("accurate", message.getParseParams().getOcrMode());
        assertEquals("OCR", message.getParseParams().getOcrService());
    }

    @Test
    public void testLocalDateTimeSerialization() throws Exception {
        // 创建一个ResumeParseMessage对象
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId("test-message-id");
        message.setBatchId("test-batch-id");
        message.setFileName("test.pdf");
        message.setFileType("pdf");
        message.setFileSize(1024L);
        message.setPriority(5);
        message.setCreateTime(LocalDateTime.of(2025, 7, 2, 15, 35, 13, 433611979));

        ResumeParseMessage.ParseParams parseParams = new ResumeParseMessage.ParseParams();
        parseParams.setRawtext(true);
        parseParams.setHandleImage(true);
        parseParams.setAvatar(true);
        parseParams.setParseMode("fast");
        parseParams.setOcrMode("accurate");
        parseParams.setOcrService("OCR");
        message.setParseParams(parseParams);

        // 测试序列化
        String json = objectMapper.writeValueAsString(message);
        assertNotNull(json);
        System.out.println("序列化后的JSON: " + json);

        // 检查序列化格式（可能是数组格式而不是ISO字符串格式）
        assertTrue(json.contains("createTime"), "JSON应该包含createTime字段");

        // 测试反序列化
        ResumeParseMessage deserializedMessage = objectMapper.readValue(json, ResumeParseMessage.class);
        assertNotNull(deserializedMessage);
        assertEquals(message.getMessageId(), deserializedMessage.getMessageId());
        assertEquals(message.getCreateTime(), deserializedMessage.getCreateTime());
    }
}
