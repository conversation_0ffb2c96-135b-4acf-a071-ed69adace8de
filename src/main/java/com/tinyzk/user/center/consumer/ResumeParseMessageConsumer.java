package com.tinyzk.user.center.consumer;

import com.alibaba.fastjson2.JSON;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import com.tinyzk.user.center.service.OSSFileStorageService;
import com.tinyzk.user.center.service.ResumePersistenceService;
import com.tinyzk.user.center.service.ThirdPartyResumeParseService;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
// 注释掉旧的RocketMQ Spring Boot Starter注解
// import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
// import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;

/**
 * 简历解析消息消费者
 * 注意：已迁移到阿里云官方客户端，由AliyunConsumerService统一管理
 * 此类保留业务逻辑，但不再直接作为消费者
 */
@Component
@Slf4j
// 注释掉旧的RocketMQ注解，改为由AliyunConsumerService调用
/*
@RocketMQMessageListener(
    topic = "RESUME_PARSE_TOPIC",
    consumerGroup = "resume-parse-consumer-group",
    consumeMode = org.apache.rocketmq.spring.annotation.ConsumeMode.CONCURRENTLY, // 并发消费，提高性能
    maxReconsumeTimes = 3, // 最大重试次数
    consumeThreadMax = 20, // 最大消费线程数
    messageModel = org.apache.rocketmq.spring.annotation.MessageModel.CLUSTERING, // 集群消费模式
    selectorExpression = "*" // 订阅所有Tag
)
*/
public class ResumeParseMessageConsumer { // 移除 implements RocketMQListener<ResumeParseMessage>

    private final ThirdPartyResumeParseService parseService;
    private final OSSFileStorageService ossService;
    private final ResumePersistenceService persistenceService;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final RateLimiter consumerLimiter;
    private final CircuitBreaker apiCircuitBreaker;
    private final MeterRegistry meterRegistry;
    public ResumeParseMessageConsumer(ThirdPartyResumeParseService parseService,
                                    OSSFileStorageService ossService,
                                    ResumePersistenceService persistenceService,
                                    ResumeParseRecordsMapper parseRecordsMapper,
                                    MeterRegistry meterRegistry) {
        log.info("初始化简历解析消息消费者...");
        this.parseService = parseService;
        this.ossService = ossService;
        this.persistenceService = persistenceService;
        this.parseRecordsMapper = parseRecordsMapper;
        this.meterRegistry = meterRegistry;
        // 使用Resilience4j的RateLimiter
        this.consumerLimiter = RateLimiter.of("message-consumer",
            RateLimiterConfig.custom()
                .limitForPeriod(30)
                .limitRefreshPeriod(java.time.Duration.ofSeconds(1))
                .timeoutDuration(java.time.Duration.ofMillis(100))
                .build());

        // 使用Resilience4j的CircuitBreaker
        this.apiCircuitBreaker = CircuitBreaker.of("third-party-api",
            CircuitBreakerConfig.custom()
                .failureRateThreshold(60)
                .waitDurationInOpenState(java.time.Duration.ofSeconds(60))
                .slidingWindowSize(10)
                .minimumNumberOfCalls(5)
                .build());

        log.info("简历解析消息消费者初始化完成，监听主题: RESUME_PARSE_TOPIC, 消费者组: resume-parse-consumer-group");
    }

    // 移除@Override注解，改为普通的业务处理方法
    public void processMessage(ResumeParseMessage message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        String messageId = message.getMessageId();

        try {
            log.info("开始处理简历解析消息: messageId={}, batchId={}, fileName={}",
                    messageId, message.getBatchId(), message.getFileName());

            // 使用Resilience4j限流控制
            consumerLimiter.executeRunnable(() -> {
                processResumeParseMessage(message);
            });

            meterRegistry.counter("mq.message.consume.success",
                "topic", "RESUME_PARSE_TOPIC").increment();

            log.info("简历解析消息处理完成: messageId={}", messageId);

        } catch (CallNotPermittedException e) {
            log.warn("第三方API熔断，消息将重试: messageId={}", messageId);
            meterRegistry.counter("mq.message.consume.circuit_breaker",
                "topic", "RESUME_PARSE_TOPIC").increment();
            throw new RuntimeException("API熔断，触发重试", e);

        } catch (Exception e) {
            log.error("处理简历解析消息失败: messageId={}", messageId, e);
            meterRegistry.counter("mq.message.consume.failure",
                "topic", "RESUME_PARSE_TOPIC").increment();

            // 根据错误类型决定是否重试
            if (isRetryableError(e)) {
                throw e; // 触发重试
            } else {
                // 不可重试错误，记录到死信队列
                handleNonRetryableError(message, e);
            }
        } finally {
            sample.stop(Timer.builder("mq.message.consume.duration")
                .tag("topic", "RESUME_PARSE_TOPIC")
                .register(meterRegistry));
        }
    }

    /**
     * 处理简历解析消息的核心逻辑
     */
    private void processResumeParseMessage(ResumeParseMessage message) {
        Long parseRecordId = null;
        
        try {
            // 1. 创建解析记录
            parseRecordId = createParseRecord(message);
            
            // 2. 从OSS下载文件
            byte[] fileContent = downloadFileFromOSS(message);
            
            // 3. 创建MultipartFile对象
            MultipartFile multipartFile = createMultipartFile(message, fileContent);
            
            // 4. 调用第三方API解析简历
            ThirdPartyParseResultDTO parseResult = callThirdPartyApiWithCircuitBreaker(multipartFile);
            
            // 5. 更新解析记录状态
            updateParseRecordWithResult(parseRecordId, parseResult);
            
            // 6. 转换并保存解析数据
            if (message.getUserId() != null) {
                persistenceService.saveConvertedData(message.getUserId(), parseResult, message);
            }
            
            log.info("简历解析处理成功: messageId={}, parseRecordId={}", 
                    message.getMessageId(), parseRecordId);
                    
        } catch (Exception e) {
            // 更新解析记录为失败状态
            if (parseRecordId != null) {
                updateParseRecordWithError(parseRecordId, e);
            }
            throw e;
        }
    }

    /**
     * 创建解析记录
     */
    private Long createParseRecord(ResumeParseMessage message) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setUserId(message.getUserId());
        record.setOriginalFilename(message.getFileName());
        record.setFileSize(message.getFileSize());
        record.setFileType(message.getFileType());
        record.setParseStatus(1); // 解析中
        record.setCreatedAt(LocalDateTime.now());
        
        parseRecordsMapper.insert(record);
        return record.getRecordId();
    }

    /**
     * 从OSS下载文件
     */
    private byte[] downloadFileFromOSS(ResumeParseMessage message) {
        try {
            return ossService.downloadFile(message.getOssKey());
        } catch (Exception e) {
            log.error("从OSS下载文件失败: ossKey={}", message.getOssKey(), e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    /**
     * 创建MultipartFile对象
     */
    private MultipartFile createMultipartFile(ResumeParseMessage message, byte[] content) {
        return new MultipartFile() {
            @Override
            public String getName() { return "file"; }

            @Override
            public String getOriginalFilename() { return message.getFileName(); }

            @Override
            public String getContentType() { return ResumeParseMessageConsumer.this.getContentType(message.getFileType()); }

            @Override
            public boolean isEmpty() { return content.length == 0; }

            @Override
            public long getSize() { return content.length; }

            @Override
            public byte[] getBytes() { return content; }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(content);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException {
                throw new UnsupportedOperationException("transferTo not supported");
            }
        };
    }

    /**
     * 使用熔断器保护的第三方API调用
     */
    private ThirdPartyParseResultDTO callThirdPartyApiWithCircuitBreaker(MultipartFile file) {
        return apiCircuitBreaker.executeSupplier(() -> {
            return parseService.parseResumeWithRetry(file);
        });
    }

    /**
     * 更新解析记录（成功）
     */
    private void updateParseRecordWithResult(Long recordId, ThirdPartyParseResultDTO result) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(result.getErrorCode() == 0 ? 2 : 3); // 2-成功, 3-失败
            record.setParseResult(JSON.toJSONString(result));
            record.setThirdPartyId(result.getCvId());
            record.setErrorMessage(result.getErrorMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败: recordId={}", recordId, e);
        }
    }

    /**
     * 更新解析记录（失败）
     */
    private void updateParseRecordWithError(Long recordId, Exception error) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(3); // 失败
            record.setErrorMessage(error.getMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败状态失败: recordId={}", recordId, e);
        }
    }

    

    /**
     * 判断是否为可重试错误
     */
    private boolean isRetryableError(Exception e) {
        return e instanceof SocketTimeoutException ||
               e instanceof ConnectException ||
               e instanceof org.springframework.web.client.HttpServerErrorException ||
               (e instanceof RuntimeException && e.getMessage().contains("API熔断"));
    }

    /**
     * 处理不可重试错误
     */
    private void handleNonRetryableError(ResumeParseMessage message, Exception e) {
        try {
            log.error("不可重试错误，记录到死信队列: messageId={}, error={}",
                    message.getMessageId(), e.getMessage());

            // 记录死信消息到数据库或其他存储
            // 这里可以扩展为发送到死信队列主题
            meterRegistry.counter("mq.message.dead_letter",
                "topic", "RESUME_PARSE_TOPIC").increment();

        } catch (Exception ex) {
            log.error("处理死信消息失败: messageId={}", message.getMessageId(), ex);
        }
    }

    /**
     * 根据文件类型获取Content-Type
     */
    private String getContentType(String fileType) {
        if (fileType == null) {
            return "application/octet-stream";
        }

        switch (fileType.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }
}
