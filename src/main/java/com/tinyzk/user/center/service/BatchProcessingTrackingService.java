package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 批量处理进度跟踪服务
 * 提供批量操作的进度跟踪、状态管理和实时监控功能
 */
@Service
@Slf4j
public class BatchProcessingTrackingService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final MeterRegistry meterRegistry;
    
    // 内存缓存，用于快速访问活跃的批次信息
    private final ConcurrentMap<String, BatchProgress> activeProgressCache = new ConcurrentHashMap<>();
    
    private static final String BATCH_PROGRESS_PREFIX = "batch:progress:";
    private static final String BATCH_RESULT_PREFIX = "batch:result:";
    private static final Duration CACHE_EXPIRY = Duration.ofHours(24);

    public BatchProcessingTrackingService(RedisTemplate<String, Object> redisTemplate,
                                        MeterRegistry meterRegistry) {
        this.redisTemplate = redisTemplate;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 生成批次ID
     */
    public String generateBatchId() {
        return "batch_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 初始化批量处理进度
     */
    public BatchProgress initializeBatchProgress(String batchId, int totalCount, String operationType) {
        BatchProgress progress = new BatchProgress();
        progress.setBatchId(batchId);
        progress.setTotalCount(totalCount);
        progress.setProcessedCount(0);
        progress.setSuccessCount(0);
        progress.setFailureCount(0);
        progress.setStatus(BatchStatus.PROCESSING);
        progress.setOperationType(operationType);
        progress.setStartTime(LocalDateTime.now());
        progress.setLastUpdateTime(LocalDateTime.now());

        // 保存到Redis
        String key = BATCH_PROGRESS_PREFIX + batchId;
        redisTemplate.opsForValue().set(key, progress, CACHE_EXPIRY);
        
        // 添加到内存缓存
        activeProgressCache.put(batchId, progress);

        log.info("批量处理进度初始化: batchId={}, totalCount={}, operationType={}", 
                batchId, totalCount, operationType);
        
        // 更新监控指标
        meterRegistry.counter("batch.processing.initialized", "operation_type", operationType).increment();
        meterRegistry.gauge("batch.processing.total_count", Tags.of(Tag.of("batch_id", batchId)), totalCount);

        return progress;
    }

    /**
     * 更新批量处理进度
     */
    public void updateBatchProgress(String batchId, boolean success, String errorMessage) {
        BatchProgress progress = getBatchProgressFromCache(batchId);
        
        if (progress != null) {
            synchronized (progress) {
                progress.setProcessedCount(progress.getProcessedCount() + 1);
                progress.setLastUpdateTime(LocalDateTime.now());
                
                if (success) {
                    progress.setSuccessCount(progress.getSuccessCount() + 1);
                } else {
                    progress.setFailureCount(progress.getFailureCount() + 1);
                    if (errorMessage != null) {
                        progress.getErrorMessages().add(errorMessage);
                    }
                }

                // 检查是否完成
                if (progress.getProcessedCount() >= progress.getTotalCount()) {
                    progress.setStatus(BatchStatus.COMPLETED);
                    progress.setEndTime(LocalDateTime.now());
                    
                    // 从活跃缓存中移除
                    activeProgressCache.remove(batchId);
                    
                    log.info("批量处理完成: batchId={}, 成功={}, 失败={}", 
                            batchId, progress.getSuccessCount(), progress.getFailureCount());
                }

                // 更新Redis
                String key = BATCH_PROGRESS_PREFIX + batchId;
                redisTemplate.opsForValue().set(key, progress, CACHE_EXPIRY);

                // 更新监控指标
                updateProgressMetrics(progress);
            }
        } else {
            log.warn("批次进度不存在: batchId={}", batchId);
        }
    }

    /**
     * 批量更新进度（用于批量操作的结果更新）
     */
    public void batchUpdateProgress(String batchId, List<BatchItemResult> results) {
        BatchProgress progress = getBatchProgressFromCache(batchId);
        
        if (progress != null) {
            synchronized (progress) {
                for (BatchItemResult result : results) {
                    progress.setProcessedCount(progress.getProcessedCount() + 1);
                    
                    if (result.isSuccess()) {
                        progress.setSuccessCount(progress.getSuccessCount() + 1);
                    } else {
                        progress.setFailureCount(progress.getFailureCount() + 1);
                        if (result.getErrorMessage() != null) {
                            progress.getErrorMessages().add(result.getErrorMessage());
                        }
                    }
                }
                
                progress.setLastUpdateTime(LocalDateTime.now());

                // 检查是否完成
                if (progress.getProcessedCount() >= progress.getTotalCount()) {
                    progress.setStatus(BatchStatus.COMPLETED);
                    progress.setEndTime(LocalDateTime.now());
                    activeProgressCache.remove(batchId);
                }

                // 更新Redis
                String key = BATCH_PROGRESS_PREFIX + batchId;
                redisTemplate.opsForValue().set(key, progress, CACHE_EXPIRY);

                // 更新监控指标
                updateProgressMetrics(progress);
            }
        }
    }

    /**
     * 获取批量处理进度
     */
    public BatchProgress getBatchProgress(String batchId) {
        // 先从内存缓存获取
        BatchProgress progress = activeProgressCache.get(batchId);
        if (progress != null) {
            return progress;
        }

        // 从Redis获取
        String key = BATCH_PROGRESS_PREFIX + batchId;
        Object redisValue = redisTemplate.opsForValue().get(key);
        progress = convertToBatchProgress(redisValue);
        
        if (progress != null && progress.getStatus() == BatchStatus.PROCESSING) {
            // 如果是处理中状态，添加到内存缓存
            activeProgressCache.put(batchId, progress);
        }
        
        return progress;
    }

    /**
     * 标记批次为失败状态
     */
    public void markBatchAsFailed(String batchId, String errorMessage) {
        BatchProgress progress = getBatchProgressFromCache(batchId);
        
        if (progress != null) {
            synchronized (progress) {
                progress.setStatus(BatchStatus.FAILED);
                progress.setEndTime(LocalDateTime.now());
                progress.setLastUpdateTime(LocalDateTime.now());
                
                if (errorMessage != null) {
                    progress.getErrorMessages().add(errorMessage);
                }

                // 更新Redis
                String key = BATCH_PROGRESS_PREFIX + batchId;
                redisTemplate.opsForValue().set(key, progress, CACHE_EXPIRY);

                // 从活跃缓存中移除
                activeProgressCache.remove(batchId);

                log.error("批量处理失败: batchId={}, error={}", batchId, errorMessage);
                
                // 更新监控指标
                meterRegistry.counter("batch.processing.failed", 
                    "operation_type", progress.getOperationType()).increment();
            }
        }
    }

    /**
     * 获取所有活跃的批次进度
     */
    public List<BatchProgress> getActiveBatchProgresses() {
        return activeProgressCache.values().stream().toList();
    }

    /**
     * 清理过期的批次记录
     */
    public void cleanupExpiredBatches() {
        try {
            // 清理内存缓存中超过1小时未更新的记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1);
            activeProgressCache.entrySet().removeIf(entry -> 
                entry.getValue().getLastUpdateTime().isBefore(cutoffTime));
            
            log.debug("清理过期批次记录完成，当前活跃批次数: {}", activeProgressCache.size());
            
        } catch (Exception e) {
            log.error("清理过期批次记录失败", e);
        }
    }

    /**
     * 获取批次统计信息
     */
    public BatchStatistics getBatchStatistics() {
        BatchStatistics stats = new BatchStatistics();
        
        int activeBatches = activeProgressCache.size();
        stats.setActiveBatchCount(activeBatches);
        
        // 计算总体统计
        int totalProcessed = 0;
        int totalSuccess = 0;
        int totalFailure = 0;
        
        for (BatchProgress progress : activeProgressCache.values()) {
            totalProcessed += progress.getProcessedCount();
            totalSuccess += progress.getSuccessCount();
            totalFailure += progress.getFailureCount();
        }
        
        stats.setTotalProcessedItems(totalProcessed);
        stats.setTotalSuccessItems(totalSuccess);
        stats.setTotalFailureItems(totalFailure);
        
        if (totalProcessed > 0) {
            stats.setOverallSuccessRate((double) totalSuccess / totalProcessed);
        }
        
        return stats;
    }

    /**
     * 从缓存获取批次进度
     */
    private BatchProgress getBatchProgressFromCache(String batchId) {
        BatchProgress progress = activeProgressCache.get(batchId);
        if (progress == null) {
            String key = BATCH_PROGRESS_PREFIX + batchId;
            progress = (BatchProgress) redisTemplate.opsForValue().get(key);
            if (progress != null && progress.getStatus() == BatchStatus.PROCESSING) {
                activeProgressCache.put(batchId, progress);
            }
        }
        return progress;
    }

    /**
     * 更新进度监控指标
     */
    private void updateProgressMetrics(BatchProgress progress) {
        String batchId = progress.getBatchId();
        String operationType = progress.getOperationType();
        
        // 更新进度百分比
        double progressRatio = progress.getTotalCount() > 0 ? 
            (double) progress.getProcessedCount() / progress.getTotalCount() : 0.0;
        
        meterRegistry.gauge("batch.processing.progress", 
            Tags.of(Tag.of("batch_id", batchId), Tag.of("operation_type", operationType)), 
            progressRatio);
        
        // 更新成功率
        if (progress.getProcessedCount() > 0) {
            double successRate = (double) progress.getSuccessCount() / progress.getProcessedCount();
            meterRegistry.gauge("batch.processing.success_rate", 
                Tags.of(Tag.of("batch_id", batchId), Tag.of("operation_type", operationType)), 
                successRate);
        }
        
        // 如果批次完成，记录完成指标
        if (progress.getStatus() == BatchStatus.COMPLETED) {
            meterRegistry.counter("batch.processing.completed", 
                "operation_type", operationType).increment();
            
            if (progress.getStartTime() != null && progress.getEndTime() != null) {
                Duration duration = Duration.between(progress.getStartTime(), progress.getEndTime());
                meterRegistry.timer("batch.processing.duration", 
                    "operation_type", operationType)
                    .record(duration);
            }
        }
    }

    /**
     * 批次状态枚举
     */
    public enum BatchStatus {
        PROCESSING,  // 处理中
        COMPLETED,   // 已完成
        FAILED       // 失败
    }

    /**
     * 批次项目结果
     */
    public static class BatchItemResult {
        private boolean success;
        private String errorMessage;
        private Object data;

        public BatchItemResult(boolean success) {
            this.success = success;
        }

        public BatchItemResult(boolean success, String errorMessage) {
            this.success = success;
            this.errorMessage = errorMessage;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }

    /**
     * 批次进度信息
     */
    public static class BatchProgress {
        private String batchId;
        private int totalCount;
        private int processedCount;
        private int successCount;
        private int failureCount;
        private BatchStatus status;
        private String operationType;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private LocalDateTime lastUpdateTime;
        private List<String> errorMessages = new java.util.ArrayList<>();

        // Getters and Setters
        public String getBatchId() { return batchId; }
        public void setBatchId(String batchId) { this.batchId = batchId; }
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public BatchStatus getStatus() { return status; }
        public void setStatus(BatchStatus status) { this.status = status; }
        public String getOperationType() { return operationType; }
        public void setOperationType(String operationType) { this.operationType = operationType; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(LocalDateTime lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }

        /**
         * 获取进度百分比
         */
        public double getProgressPercentage() {
            return totalCount > 0 ? (double) processedCount / totalCount * 100 : 0.0;
        }

        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            return processedCount > 0 ? (double) successCount / processedCount : 0.0;
        }

        /**
         * 是否已完成
         */
        public boolean isCompleted() {
            return status == BatchStatus.COMPLETED || status == BatchStatus.FAILED;
        }

        /**
         * 获取处理耗时（毫秒）
         */
        public Long getDurationMillis() {
            if (startTime == null) return null;
            LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
            return Duration.between(startTime, endTimeToUse).toMillis();
        }
    }

    /**
     * 批次统计信息
     */
    public static class BatchStatistics {
        private int activeBatchCount;
        private int totalProcessedItems;
        private int totalSuccessItems;
        private int totalFailureItems;
        private double overallSuccessRate;

        // Getters and Setters
        public int getActiveBatchCount() { return activeBatchCount; }
        public void setActiveBatchCount(int activeBatchCount) { this.activeBatchCount = activeBatchCount; }
        public int getTotalProcessedItems() { return totalProcessedItems; }
        public void setTotalProcessedItems(int totalProcessedItems) { this.totalProcessedItems = totalProcessedItems; }
        public int getTotalSuccessItems() { return totalSuccessItems; }
        public void setTotalSuccessItems(int totalSuccessItems) { this.totalSuccessItems = totalSuccessItems; }
        public int getTotalFailureItems() { return totalFailureItems; }
        public void setTotalFailureItems(int totalFailureItems) { this.totalFailureItems = totalFailureItems; }
        public double getOverallSuccessRate() { return overallSuccessRate; }
        public void setOverallSuccessRate(double overallSuccessRate) { this.overallSuccessRate = overallSuccessRate; }
    }

    /**
     * 将Redis返回的对象转换为BatchProgress
     * 处理GenericJackson2JsonRedisSerializer反序列化为LinkedHashMap的问题
     */
    private BatchProgress convertToBatchProgress(Object redisValue) {
        if (redisValue == null) {
            return null;
        }

        if (redisValue instanceof BatchProgress) {
            return (BatchProgress) redisValue;
        }

        if (redisValue instanceof java.util.Map) {
            try {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> map = (java.util.Map<String, Object>) redisValue;

                BatchProgress progress = new BatchProgress();
                progress.setBatchId((String) map.get("batchId"));
                progress.setTotalCount(getIntValue(map.get("totalCount")));
                progress.setProcessedCount(getIntValue(map.get("processedCount")));
                progress.setSuccessCount(getIntValue(map.get("successCount")));
                progress.setFailureCount(getIntValue(map.get("failureCount")));
                progress.setOperationType((String) map.get("operationType"));

                // 处理枚举类型
                Object statusObj = map.get("status");
                if (statusObj != null) {
                    if (statusObj instanceof BatchStatus) {
                        progress.setStatus((BatchStatus) statusObj);
                    } else {
                        progress.setStatus(BatchStatus.valueOf(statusObj.toString()));
                    }
                }

                // 处理时间类型
                progress.setStartTime(convertToLocalDateTime(map.get("startTime")));
                progress.setEndTime(convertToLocalDateTime(map.get("endTime")));
                progress.setLastUpdateTime(convertToLocalDateTime(map.get("lastUpdateTime")));

                // 处理错误消息列表
                Object errorMessagesObj = map.get("errorMessages");
                if (errorMessagesObj instanceof java.util.List) {
                    @SuppressWarnings("unchecked")
                    java.util.List<String> errorMessages = (java.util.List<String>) errorMessagesObj;
                    progress.setErrorMessages(errorMessages);
                }

                return progress;
            } catch (Exception e) {
                log.error("转换BatchProgress失败: {}", e.getMessage(), e);
                return null;
            }
        }

        log.warn("无法转换Redis值为BatchProgress: {}", redisValue.getClass().getName());
        return null;
    }

    /**
     * 安全地获取整数值
     */
    private int getIntValue(Object value) {
        if (value == null) return 0;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 转换为LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Object value) {
        if (value == null) return null;
        if (value instanceof LocalDateTime) return (LocalDateTime) value;
        if (value instanceof String) {
            try {
                return LocalDateTime.parse((String) value);
            } catch (Exception e) {
                log.warn("无法解析时间字符串: {}", value);
                return null;
            }
        }
        if (value instanceof java.util.List) {
            // Jackson可能将LocalDateTime序列化为数组 [year, month, day, hour, minute, second, nano]
            try {
                @SuppressWarnings("unchecked")
                java.util.List<Integer> timeArray = (java.util.List<Integer>) value;
                if (timeArray.size() >= 6) {
                    return LocalDateTime.of(
                        timeArray.get(0), // year
                        timeArray.get(1), // month
                        timeArray.get(2), // day
                        timeArray.get(3), // hour
                        timeArray.get(4), // minute
                        timeArray.get(5), // second
                        timeArray.size() > 6 ? timeArray.get(6) : 0 // nano
                    );
                }
            } catch (Exception e) {
                log.warn("无法从数组解析时间: {}", value);
            }
        }
        return null;
    }
}
