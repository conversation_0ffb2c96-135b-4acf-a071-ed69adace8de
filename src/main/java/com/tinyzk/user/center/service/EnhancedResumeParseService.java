package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.retry.Retry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 增强的简历解析服务
 * 集成消息队列、熔断器、限流器等功能
 */
@Service
@Slf4j
public class EnhancedResumeParseService {

    private final ThirdPartyResumeParseService thirdPartyService;
    private final MigratedMessageProducerService messageProducer;
    private final OSSFileStorageService ossService;
    private final BatchDatabaseService batchDatabaseService;
    private final BackpressureControlService backpressureService;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final ThreadPoolTaskExecutor apiCallExecutor;
    private final MeterRegistry meterRegistry;

    // Resilience4j组件
    private final CircuitBreaker apiCircuitBreaker;
    private final RateLimiter apiRateLimiter;
    private final Retry apiRetry;

    // 统计指标
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger totalSucceeded = new AtomicInteger(0);
    private final AtomicInteger totalFailed = new AtomicInteger(0);

    public EnhancedResumeParseService(ThirdPartyResumeParseService thirdPartyService,
                                    MigratedMessageProducerService messageProducer,
                                    OSSFileStorageService ossService,
                                    BatchDatabaseService batchDatabaseService,
                                    BackpressureControlService backpressureService,
                                    ResumeParseRecordsMapper parseRecordsMapper,
                                    @Qualifier("apiCallExecutor") ThreadPoolTaskExecutor executor,
                                    MeterRegistry meterRegistry) {
        this.thirdPartyService = thirdPartyService;
        this.messageProducer = messageProducer;
        this.ossService = ossService;
        this.batchDatabaseService = batchDatabaseService;
        this.backpressureService = backpressureService;
        this.parseRecordsMapper = parseRecordsMapper;
        this.apiCallExecutor = executor;
        this.meterRegistry = meterRegistry;

        // 初始化Resilience4j组件
        this.apiCircuitBreaker = CircuitBreaker.ofDefaults("resume-parse-api");
        this.apiRateLimiter = RateLimiter.ofDefaults("resume-parse-api");
        this.apiRetry = Retry.ofDefaults("resume-parse-api");
    }

    /**
     * 异步批量简历解析
     */
    public CompletableFuture<BatchParseResult> batchParseResumesAsync(List<ResumeParseMessage> messages) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);
            BatchParseResult result = new BatchParseResult();

            try {
                log.info("开始批量简历解析: 数量={}", messages.size());

                // 检查背压控制
                if (!backpressureService.shouldEnableBackpressure()) {
                    result = processBatchMessages(messages);
                } else {
                    log.warn("系统负载过高，启用背压控制，部分消息将延迟处理");
                    result = processBatchMessagesWithBackpressure(messages);
                }

                log.info("批量简历解析完成: 总数={}, 成功={}, 失败={}", 
                        result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

                return result;

            } catch (Exception e) {
                log.error("批量简历解析失败", e);
                result.setTotalCount(messages.size());
                result.setFailureCount(messages.size());
                meterRegistry.counter("resume.parse.batch.error").increment();
                return result;
            } finally {
                sample.stop(Timer.builder("resume.parse.batch.duration")
                    .tag("batch_size", String.valueOf(messages.size()))
                    .register(meterRegistry));
            }
        }, apiCallExecutor);
    }

    /**
     * 处理批量消息
     */
    private BatchParseResult processBatchMessages(List<ResumeParseMessage> messages) {
        BatchParseResult result = new BatchParseResult();
        result.setTotalCount(messages.size());

        for (ResumeParseMessage message : messages) {
            try {
                // 发送到消息队列进行异步处理
                messageProducer.sendResumeParseMessage(message);
                result.incrementSuccess();
                
                meterRegistry.counter("resume.parse.message.sent").increment();
                
            } catch (Exception e) {
                log.error("发送简历解析消息失败: messageId={}", message.getMessageId(), e);
                result.incrementFailure();
                meterRegistry.counter("resume.parse.message.send.failed").increment();
            }
        }

        return result;
    }

    /**
     * 带背压控制的批量消息处理
     */
    private BatchParseResult processBatchMessagesWithBackpressure(List<ResumeParseMessage> messages) {
        BatchParseResult result = new BatchParseResult();
        result.setTotalCount(messages.size());

        for (ResumeParseMessage message : messages) {
            // 尝试获取处理许可
            if (backpressureService.tryAcquirePermit()) {
                try {
                    messageProducer.sendResumeParseMessage(message);
                    result.incrementSuccess();
                    meterRegistry.counter("resume.parse.message.sent.backpressure").increment();
                    
                } catch (Exception e) {
                    log.error("背压控制下发送消息失败: messageId={}", message.getMessageId(), e);
                    result.incrementFailure();
                }
            } else {
                // 背压控制拒绝，延迟处理
                result.incrementDelayed();
                meterRegistry.counter("resume.parse.message.delayed").increment();
                log.debug("消息因背压控制被延迟: messageId={}", message.getMessageId());
            }
        }

        return result;
    }

    /**
     * 直接解析简历（同步）
     */
    public ThirdPartyParseResultDTO parseResumeDirectly(MultipartFile file) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            // 使用Resilience4j保护API调用
            return apiCircuitBreaker.executeSupplier(() ->
                apiRateLimiter.executeSupplier(() ->
                    apiRetry.executeSupplier(() ->
                        thirdPartyService.parseResumeWithRetry(file)
                    )
                )
            );

        } catch (Exception e) {
            log.error("直接解析简历失败", e);
            meterRegistry.counter("resume.parse.direct.failed").increment();
            throw new RuntimeException("简历解析失败", e);
        } finally {
            sample.stop(Timer.builder("resume.parse.direct.duration")
                .register(meterRegistry));
        }
    }

    /**
     * 批量上传并解析简历
     */
    public CompletableFuture<BatchParseResult> batchUploadAndParse(List<FileUploadRequest> requests, String batchId) {
        return CompletableFuture.supplyAsync(() -> {
            Timer.Sample sample = Timer.start(meterRegistry);
            BatchParseResult result = new BatchParseResult();

            try {
                log.info("开始批量上传并解析: 批次ID={}, 文件数量={}", batchId, requests.size());

                // 1. 批量上传到OSS
                List<String> uploadResults = ossService.batchUpload(
                    requests.stream()
                        .map(req -> new OSSFileStorageService.FileUploadRequest(
                            req.getFileName(), req.getContent(), "resume"))
                        .collect(java.util.stream.Collectors.toList())
                ).get();

                // 2. 创建解析消息
                List<ResumeParseMessage> messages = createParseMessages(requests, uploadResults, batchId);

                // 3. 批量处理解析消息
                result = processBatchMessages(messages);

                log.info("批量上传并解析完成: 批次ID={}, 结果={}", batchId, result);
                return result;

            } catch (Exception e) {
                log.error("批量上传并解析失败: 批次ID={}", batchId, e);
                result.setTotalCount(requests.size());
                result.setFailureCount(requests.size());
                return result;
            } finally {
                sample.stop(Timer.builder("resume.parse.batch.upload_and_parse.duration")
                    .tag("batch_id", batchId)
                    .tag("file_count", String.valueOf(requests.size()))
                    .register(meterRegistry));
            }
        }, apiCallExecutor);
    }

    /**
     * 创建解析消息
     */
    private List<ResumeParseMessage> createParseMessages(List<FileUploadRequest> requests, 
                                                       List<String> uploadResults, String batchId) {
        List<ResumeParseMessage> messages = new java.util.ArrayList<>();

        for (int i = 0; i < requests.size(); i++) {
            FileUploadRequest request = requests.get(i);
            String uploadResult = i < uploadResults.size() ? uploadResults.get(i) : null;

            if (uploadResult != null) {
                ResumeParseMessage message = new ResumeParseMessage();
                message.setMessageId(generateMessageId());
                message.setBatchId(batchId);
                message.setFileName(request.getFileName());
                message.setFileType(getFileExtension(request.getFileName()));
                message.setFileSize((long) request.getContent().length);
                message.setFileUrl(uploadResult);
                message.setOssKey(extractOssKeyFromUrl(uploadResult));
                message.setCreateTime(LocalDateTime.now());
                message.setUserId(request.getUserId());

                // 设置解析参数
                ResumeParseMessage.ParseParams params = new ResumeParseMessage.ParseParams();
                params.setRawtext(true);
                params.setHandleImage(true);
                params.setAvatar(true);
                params.setParseMode("fast");
                params.setOcrMode("accurate");
                message.setParseParams(params);

                messages.add(message);
            }
        }

        return messages;
    }

    /**
     * 获取解析统计信息
     */
    public ParseStatistics getParseStatistics() {
        ParseStatistics stats = new ParseStatistics();
        stats.setTotalProcessed(totalProcessed.get());
        stats.setTotalSucceeded(totalSucceeded.get());
        stats.setTotalFailed(totalFailed.get());
        
        // 从数据库获取更详细的统计
        try {
            stats.setTodayProcessed(parseRecordsMapper.countByDateRange(
                LocalDateTime.now().toLocalDate().atStartOfDay(),
                LocalDateTime.now()
            ));
            stats.setPendingCount(parseRecordsMapper.countByStatus(1)); // 解析中
            stats.setSuccessCount(parseRecordsMapper.countByStatus(2)); // 成功
            stats.setFailureCount(parseRecordsMapper.countByStatus(3)); // 失败
        } catch (Exception e) {
            log.warn("获取数据库统计信息失败", e);
        }
        
        return stats;
    }

    // 辅助方法
    private String generateMessageId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }

    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    private String extractOssKeyFromUrl(String fileUrl) {
        // 从文件URL中提取OSS对象键
        // 这里需要根据实际的URL格式来实现
        return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
    }

    /**
     * 批量解析结果
     */
    public static class BatchParseResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private int delayedCount;

        public void incrementSuccess() { successCount++; }
        public void incrementFailure() { failureCount++; }
        public void incrementDelayed() { delayedCount++; }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public int getDelayedCount() { return delayedCount; }
        public void setDelayedCount(int delayedCount) { this.delayedCount = delayedCount; }
    }

    /**
     * 文件上传请求
     */
    public static class FileUploadRequest {
        private String fileName;
        private byte[] content;
        private Long userId;

        public FileUploadRequest(String fileName, byte[] content, Long userId) {
            this.fileName = fileName;
            this.content = content;
            this.userId = userId;
        }

        // Getters
        public String getFileName() { return fileName; }
        public byte[] getContent() { return content; }
        public Long getUserId() { return userId; }
    }

    /**
     * 解析统计信息
     */
    public static class ParseStatistics {
        private int totalProcessed;
        private int totalSucceeded;
        private int totalFailed;
        private int todayProcessed;
        private int pendingCount;
        private int successCount;
        private int failureCount;

        // Getters and Setters
        public int getTotalProcessed() { return totalProcessed; }
        public void setTotalProcessed(int totalProcessed) { this.totalProcessed = totalProcessed; }
        public int getTotalSucceeded() { return totalSucceeded; }
        public void setTotalSucceeded(int totalSucceeded) { this.totalSucceeded = totalSucceeded; }
        public int getTotalFailed() { return totalFailed; }
        public void setTotalFailed(int totalFailed) { this.totalFailed = totalFailed; }
        public int getTodayProcessed() { return todayProcessed; }
        public void setTodayProcessed(int todayProcessed) { this.todayProcessed = todayProcessed; }
        public int getPendingCount() { return pendingCount; }
        public void setPendingCount(int pendingCount) { this.pendingCount = pendingCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }

        public double getSuccessRate() {
            return totalProcessed > 0 ? (double) totalSucceeded / totalProcessed : 0.0;
        }
    }
}
