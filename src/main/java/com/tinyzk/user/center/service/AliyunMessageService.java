package com.tinyzk.user.center.service;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 阿里云RocketMQ消息服务
 * 基于官方TCP示例的最佳实践
 */
@Service
@Slf4j
public class AliyunMessageService {

    private final Producer producer;

    // 项目使用的Topic常量
    public static final String RESUME_PARSE_TOPIC = "RESUME_PARSE_TOPIC";
    public static final String FILE_UPLOAD_TOPIC = "FILE_UPLOAD_TOPIC";
    
    // 消息标签
    public static final String RESUME_PARSE_TAG = "resume_parse";
    public static final String FILE_UPLOAD_TAG = "file_upload";

    public AliyunMessageService(@Qualifier("aliyunProducer") Producer producer) {
        this.producer = producer;
    }

    /**
     * 发送简历解析消息
     * 基于官方Demo的发送方式
     */
    public boolean sendResumeParseMessage(String messageBody) {
        return sendMessage(RESUME_PARSE_TOPIC, RESUME_PARSE_TAG, messageBody);
    }

    /**
     * 发送文件上传消息
     */
    public boolean sendFileUploadMessage(String messageBody) {
        return sendMessage(FILE_UPLOAD_TOPIC, FILE_UPLOAD_TAG, messageBody);
    }

    /**
     * 通用消息发送方法
     * 完全按照官方Demo的方式实现
     */
    private boolean sendMessage(String topic, String tag, String messageBody) {
        try {
            // 按照官方Demo创建消息
            Message message = new Message(topic, tag, messageBody.getBytes());
            
            // 发送消息
            SendResult sendResult = producer.send(message);
            
            if (sendResult != null) {
                log.info("{} 发送消息成功! Topic: {}, Tag: {}, MsgId: {}", 
                        new Date(), topic, tag, sendResult.getMessageId());
                return true;
            } else {
                log.error("{} 发送消息失败! Topic: {}, Tag: {}, SendResult为null", 
                        new Date(), topic, tag);
                return false;
            }
            
        } catch (ONSClientException e) {
            // 按照官方Demo的异常处理方式
            log.error("{} 发送消息失败! Topic: {}, Tag: {}, 错误信息: {}", 
                    new Date(), topic, tag, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("{} 发送消息异常! Topic: {}, Tag: {}", 
                    new Date(), topic, tag, e);
            return false;
        }
    }

    /**
     * 批量发送消息
     * 适用于简历批量解析场景
     */
    public int sendBatchMessages(String topic, String tag, String[] messageBodies) {
        int successCount = 0;
        
        for (String messageBody : messageBodies) {
            if (sendMessage(topic, tag, messageBody)) {
                successCount++;
            }
        }
        
        log.info("批量发送消息完成，成功: {}, 总数: {}", successCount, messageBodies.length);
        return successCount;
    }

    /**
     * 获取生产者状态信息
     */
    public boolean isProducerRunning() {
        try {
            // 尝试发送一个测试消息来检查生产者状态
            return producer != null;
        } catch (Exception e) {
            log.error("检查生产者状态失败", e);
            return false;
        }
    }
}
