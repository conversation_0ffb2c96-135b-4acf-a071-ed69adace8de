package com.tinyzk.user.center.service;

import com.alibaba.fastjson2.JSON;
import com.tinyzk.user.center.entity.DeadLetterRecord;
import com.tinyzk.user.center.mapper.DeadLetterRecordMapper;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
// 迁移到阿里云RocketMQ客户端
// import org.apache.rocketmq.client.producer.DefaultMQProducer;
// import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 死信队列处理服务
 */
@Service
@Slf4j
public class DeadLetterQueueService {

    private final AliyunMessageService aliyunMessageService;
    private final DeadLetterRecordMapper deadLetterRecordMapper;
    private final MeterRegistry meterRegistry;

    // 死信队列主题
    public static final String DEAD_LETTER_TOPIC = "DEAD_LETTER_TOPIC";
    public static final String RETRY_TOPIC = "RETRY_TOPIC";

    public DeadLetterQueueService(AliyunMessageService aliyunMessageService,
                                 DeadLetterRecordMapper deadLetterRecordMapper,
                                 MeterRegistry meterRegistry) {
        this.aliyunMessageService = aliyunMessageService;
        this.deadLetterRecordMapper = deadLetterRecordMapper;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 发送消息到死信队列
     */
    @Async("taskExecutor")
    public void sendToDeadLetterQueue(String originalTopic, Object originalMessage, 
                                    String errorMessage, int retryCount) {
        try {
            // 记录死信消息到数据库
            Long deadLetterId = recordDeadLetterMessage(originalTopic, originalMessage, errorMessage, retryCount);
            
            // 发送到死信队列主题 - 使用阿里云客户端
            String deadLetterMessageBody = JSON.toJSONString(
                createDeadLetterWrapper(deadLetterId, originalTopic, originalMessage, errorMessage)
            );

            // 使用阿里云消息服务发送死信消息
            boolean success = aliyunMessageService.sendTestMessage(deadLetterMessageBody); // 暂时使用测试消息发送

            if (!success) {
                throw new RuntimeException("发送死信消息失败");
            }
            
            log.warn("消息已发送到死信队列: originalTopic={}, deadLetterId={}, retryCount={}", 
                    originalTopic, deadLetterId, retryCount);
            
            meterRegistry.counter("dead_letter.message.sent",
                "original_topic", originalTopic).increment();
                
        } catch (Exception e) {
            log.error("发送死信消息失败: originalTopic={}", originalTopic, e);
            meterRegistry.counter("dead_letter.message.send_failed",
                "original_topic", originalTopic).increment();
        }
    }

    /**
     * 记录死信消息到数据库
     */
    private Long recordDeadLetterMessage(String originalTopic, Object originalMessage, 
                                       String errorMessage, int retryCount) {
        try {
            DeadLetterRecord record = new DeadLetterRecord();
            record.setOriginalTopic(originalTopic);
            record.setOriginalMessage(JSON.toJSONString(originalMessage));
            record.setErrorMessage(errorMessage);
            record.setRetryCount(retryCount);
            record.setStatus(0); // 0-未处理
            record.setCreatedAt(LocalDateTime.now());
            
            deadLetterRecordMapper.insert(record);
            return record.getId();
            
        } catch (Exception e) {
            log.error("记录死信消息到数据库失败", e);
            return null;
        }
    }

    /**
     * 创建死信消息包装器
     */
    private DeadLetterWrapper createDeadLetterWrapper(Long deadLetterId, String originalTopic, 
                                                    Object originalMessage, String errorMessage) {
        DeadLetterWrapper wrapper = new DeadLetterWrapper();
        wrapper.setDeadLetterId(deadLetterId);
        wrapper.setOriginalTopic(originalTopic);
        wrapper.setOriginalMessage(originalMessage);
        wrapper.setErrorMessage(errorMessage);
        wrapper.setTimestamp(LocalDateTime.now());
        return wrapper;
    }

    /**
     * 重试死信消息
     */
    public void retryDeadLetterMessage(Long deadLetterId) {
        try {
            DeadLetterRecord record = deadLetterRecordMapper.selectById(deadLetterId);
            if (record == null) {
                log.warn("死信记录不存在: deadLetterId={}", deadLetterId);
                return;
            }
            
            if (record.getStatus() != 0) {
                log.warn("死信记录状态不正确: deadLetterId={}, status={}", deadLetterId, record.getStatus());
                return;
            }
            
            // 发送到重试主题 - 使用阿里云客户端
            String retryMessageBody = record.getOriginalMessage();

            // 使用阿里云消息服务发送重试消息
            boolean success = aliyunMessageService.sendTestMessage(retryMessageBody); // 暂时使用测试消息发送

            if (!success) {
                throw new RuntimeException("发送重试消息失败");
            }
            
            // 更新死信记录状态
            record.setStatus(1); // 1-重试中
            record.setRetryAt(LocalDateTime.now());
            deadLetterRecordMapper.updateById(record);
            
            log.info("死信消息已发送重试: deadLetterId={}, originalTopic={}", 
                    deadLetterId, record.getOriginalTopic());
            
            meterRegistry.counter("dead_letter.message.retry",
                "original_topic", record.getOriginalTopic()).increment();
                
        } catch (Exception e) {
            log.error("重试死信消息失败: deadLetterId={}", deadLetterId, e);
            meterRegistry.counter("dead_letter.message.retry_failed").increment();
        }
    }

    /**
     * 批量重试死信消息
     */
    public void batchRetryDeadLetterMessages(List<Long> deadLetterIds) {
        for (Long deadLetterId : deadLetterIds) {
            try {
                retryDeadLetterMessage(deadLetterId);
            } catch (Exception e) {
                log.error("批量重试死信消息失败: deadLetterId={}", deadLetterId, e);
            }
        }
    }

    /**
     * 定期清理已处理的死信记录
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupProcessedDeadLetters() {
        try {
            // 清理30天前已处理的死信记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            
            int deletedCount = deadLetterRecordMapper.deleteProcessedBefore(cutoffTime);
            
            log.info("清理已处理的死信记录完成: 删除数量={}", deletedCount);
            meterRegistry.counter("dead_letter.cleanup.processed").increment(deletedCount);
            
        } catch (Exception e) {
            log.error("清理死信记录失败", e);
            meterRegistry.counter("dead_letter.cleanup.failed").increment();
        }
    }

    /**
     * 获取死信统计信息
     */
    public DeadLetterStatistics getDeadLetterStatistics() {
        try {
            DeadLetterStatistics stats = new DeadLetterStatistics();
            
            // 总数统计
            stats.setTotalCount(deadLetterRecordMapper.countTotal());
            stats.setPendingCount(deadLetterRecordMapper.countByStatus(0));
            stats.setRetryingCount(deadLetterRecordMapper.countByStatus(1));
            stats.setProcessedCount(deadLetterRecordMapper.countByStatus(2));
            stats.setFailedCount(deadLetterRecordMapper.countByStatus(3));
            
            // 按主题统计
            stats.setTopicStatistics(deadLetterRecordMapper.countByTopic());
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取死信统计信息失败", e);
            return new DeadLetterStatistics();
        }
    }

    /**
     * 死信消息包装器
     */
    public static class DeadLetterWrapper {
        private Long deadLetterId;
        private String originalTopic;
        private Object originalMessage;
        private String errorMessage;
        private LocalDateTime timestamp;

        // Getters and Setters
        public Long getDeadLetterId() { return deadLetterId; }
        public void setDeadLetterId(Long deadLetterId) { this.deadLetterId = deadLetterId; }
        public String getOriginalTopic() { return originalTopic; }
        public void setOriginalTopic(String originalTopic) { this.originalTopic = originalTopic; }
        public Object getOriginalMessage() { return originalMessage; }
        public void setOriginalMessage(Object originalMessage) { this.originalMessage = originalMessage; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }

    /**
     * 死信统计信息
     */
    public static class DeadLetterStatistics {
        private long totalCount;
        private long pendingCount;
        private long retryingCount;
        private long processedCount;
        private long failedCount;
        private java.util.Map<String, Long> topicStatistics;

        // Getters and Setters
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }
        public long getRetryingCount() { return retryingCount; }
        public void setRetryingCount(long retryingCount) { this.retryingCount = retryingCount; }
        public long getProcessedCount() { return processedCount; }
        public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }
        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }
        public java.util.Map<String, Long> getTopicStatistics() { return topicStatistics; }
        public void setTopicStatistics(java.util.Map<String, Long> topicStatistics) { this.topicStatistics = topicStatistics; }
    }
}
