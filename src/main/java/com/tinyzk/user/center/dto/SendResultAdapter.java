package com.tinyzk.user.center.dto;

import lombok.Data;

/**
 * SendResult适配器
 * 用于兼容原有的SendResult接口，避免业务代码大量修改
 */
@Data
public class SendResultAdapter {
    
    private String msgId;
    private String queueId;
    private long queueOffset;
    private String status;
    private String topic;
    private String tag;
    private long timestamp;
    
    public SendResultAdapter() {
        this.timestamp = System.currentTimeMillis();
        this.status = "SEND_OK";
        this.queueId = "0";
        this.queueOffset = 0L;
    }
    
    public SendResultAdapter(String msgId, String topic) {
        this();
        this.msgId = msgId;
        this.topic = topic;
    }
    
    public SendResultAdapter(String msgId, String topic, String tag) {
        this(msgId, topic);
        this.tag = tag;
    }
    
    // 兼容原SendResult的方法名
    public String getMessageId() {
        return msgId;
    }
    
    public void setMessageId(String messageId) {
        this.msgId = messageId;
    }
    
    // 模拟MessageQueue对象
    public MessageQueueAdapter getMessageQueue() {
        return new MessageQueueAdapter(topic, queueId);
    }
    
    // 模拟SendStatus枚举
    public SendStatusAdapter getSendStatus() {
        return SendStatusAdapter.valueOf(status);
    }
    
    public void setSendStatus(SendStatusAdapter status) {
        this.status = status.name();
    }
    
    /**
     * 内部类：模拟MessageQueue
     */
    @Data
    public static class MessageQueueAdapter {
        private String topic;
        private String queueId;
        
        public MessageQueueAdapter(String topic, String queueId) {
            this.topic = topic;
            this.queueId = queueId;
        }
        
        public int getQueueId() {
            return Integer.parseInt(queueId);
        }
    }
    
    /**
     * 内部枚举：模拟SendStatus
     */
    public enum SendStatusAdapter {
        SEND_OK,
        FLUSH_DISK_TIMEOUT,
        FLUSH_SLAVE_TIMEOUT,
        SLAVE_NOT_AVAILABLE
    }
}
