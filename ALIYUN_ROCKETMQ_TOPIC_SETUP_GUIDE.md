# 阿里云RocketMQ Topic创建指南

## 🎯 **问题描述**

当前RocketMQ迁移已经完成，但是在发送消息时遇到以下错误：

1. **Topic不存在错误**：
   ```
   No route info of this topic, MQ_INST_1708662203807002_BXfU8rhL%TEST_TOPIC
   ```

2. **连接Broker失败**：
   ```
   Connect broker failed, Topic=RESUME_PARSE_TOPIC
   ```

## 🔧 **解决方案：在阿里云控制台创建Topic**

### **步骤1：登录阿里云控制台**

1. 访问：https://ons.console.aliyun.com/
2. 登录您的阿里云账号
3. 进入消息队列RocketMQ版控制台

### **步骤2：选择实例**

1. 在实例列表中找到实例：`MQ_INST_1708662203807002_BXfU8rhL`
2. 点击实例ID进入实例详情页面

### **步骤3：创建Topic**

需要创建以下3个Topic：

#### **Topic 1: TEST_TOPIC**
- **Topic名称**：`TEST_TOPIC`
- **消息类型**：普通消息
- **备注**：测试消息Topic

#### **Topic 2: RESUME_PARSE_TOPIC**
- **Topic名称**：`RESUME_PARSE_TOPIC`
- **消息类型**：普通消息
- **备注**：简历解析消息Topic

#### **Topic 3: FILE_UPLOAD_TOPIC**
- **Topic名称**：`FILE_UPLOAD_TOPIC`
- **消息类型**：普通消息
- **备注**：文件上传消息Topic

### **步骤4：创建Consumer Group**

需要创建以下Consumer Group：

#### **Consumer Group 1: GID_user-center-consumer-group**
- **Group ID**：`GID_user-center-consumer-group`
- **协议类型**：TCP
- **备注**：用户中心消费者组

### **步骤5：验证创建结果**

创建完成后，在控制台应该能看到：

1. **Topic列表**：
   - TEST_TOPIC
   - RESUME_PARSE_TOPIC
   - FILE_UPLOAD_TOPIC

2. **Consumer Group列表**：
   - GID_user-center-consumer-group

## 🧪 **验证步骤**

### **1. 重启应用**
```bash
# 停止当前应用
# 重新启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### **2. 测试消息发送**
```bash
# 测试发送消息
curl -X POST "http://localhost:8081/api/test/aliyun-rocketmq/send-test-message?message=Hello%20World"

# 预期结果
{
  "success": true,
  "message": "消息发送成功",
  "messageBody": "Hello World",
  "timestamp": 1751439xxx
}
```

### **3. 测试简历解析消息**
```bash
# 测试简历解析消息
curl -X POST "http://localhost:8081/api/test/aliyun-rocketmq/send-resume-parse-message" \
  -d "resumeData=测试简历数据"

# 预期结果
{
  "success": true,
  "message": "简历解析消息发送成功",
  "resumeData": "测试简历数据",
  "timestamp": 1751439xxx
}
```

### **4. 测试批量消息**
```bash
# 测试批量发送
curl -X POST "http://localhost:8081/api/test/aliyun-rocketmq/send-batch-messages?count=3"

# 预期结果
{
  "success": true,
  "totalCount": 3,
  "successCount": 3,
  "failureCount": 0,
  "message": "批量发送完成，成功: 3, 失败: 0"
}
```

## 📋 **配置检查清单**

### **阿里云控制台配置**
- [ ] 实例 `MQ_INST_1708662203807002_BXfU8rhL` 存在且正常
- [ ] Topic `TEST_TOPIC` 已创建
- [ ] Topic `RESUME_PARSE_TOPIC` 已创建
- [ ] Topic `FILE_UPLOAD_TOPIC` 已创建
- [ ] Consumer Group `GID_user-center-consumer-group` 已创建

### **应用配置验证**
- [x] 阿里云RocketMQ客户端依赖正确
- [x] 生产者配置正确
- [x] 消费者配置正确
- [x] 认证信息正确
- [x] Jackson序列化问题已修复

## ⚠️ **注意事项**

### **1. 网络连接**
- 确保应用服务器能访问阿里云RocketMQ的内网地址
- 如果使用公网，需要配置安全组规则

### **2. 权限配置**
- 确保AccessKey有操作RocketMQ的权限
- 检查RAM用户权限配置

### **3. 实例类型**
- 当前配置使用的是RocketMQ 4.0系列格式（带http://前缀）
- 如果是5.0系列实例，需要去掉http://前缀

## 🚀 **完成后的效果**

Topic创建完成后，应该能够：

1. ✅ 成功发送测试消息
2. ✅ 成功发送简历解析消息
3. ✅ 成功发送文件上传消息
4. ✅ 成功接收和处理消息
5. ✅ 在控制台看到消息统计数据

## 📞 **技术支持**

如果遇到问题，可以：

1. **查看阿里云文档**：https://help.aliyun.com/product/29530.html
2. **提交工单**：通过阿里云控制台提交技术支持工单
3. **查看错误码**：https://help.aliyun.com/document_detail/29533.html

---

**创建完成后，请重新测试消息发送功能！** 🎉
