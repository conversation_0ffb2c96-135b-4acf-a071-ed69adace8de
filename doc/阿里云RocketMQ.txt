我为您提供阿里云 RocketMQ 的完整配置和示例代码：

## 一、Spring Boot 完整配置

### 1. Maven 依赖配置
```xml
<dependencies>
    <!-- 阿里云 RocketMQ Spring Boot Starter -->
    <dependency>
        <groupId>com.aliyun.openservices</groupId>
        <artifactId>ons-client</artifactId>
        <version>2.0.6.Final</version>
    </dependency>
    
    <!-- Spring Boot RocketMQ Starter -->
    <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>2.2.3</version>
    </dependency>
    
    <!-- 其他依赖 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
    </dependency>
</dependencies>
```

### 2. application.yml 完整配置
```yaml
# RocketMQ 配置
rocketmq:
  # 实例基础配置
  name-server: ${NAMESRV_ADDR:http://MQ_INST_xxxxxxxxxx.cn-hangzhou.mq.aliyuncs.com:80}
  access-key: ${ACCESS_KEY:your-access-key}
  secret-key: ${SECRET_KEY:your-secret-key}
  
  # 生产者配置
  producer:
    group: GID_PRODUCER_GROUP
    # 发送消息超时时间，单位毫秒
    send-message-timeout: 3000
    # 压缩消息体阈值，超过4K自动压缩
    compress-message-body-threshold: 4096
    # 最大消息大小，单位字节，默认4MB
    max-message-size: 4194304
    # 异步发送失败重试次数
    retry-times-when-send-async-failed: 2
    # 同步发送失败重试次数
    retry-times-when-send-failed: 2
    # 发送失败是否重试其他broker
    retry-next-server: true
    
  # 消费者配置
  consumer:
    # 消费者线程池配置
    consume-thread-min: 20
    consume-thread-max: 64
    # 单次拉取消息数量
    pull-batch-size: 32
    # 消费失败重试次数，-1表示16次
    max-reconsume-times: 16
    # 消费超时时间，单位分钟
    consume-timeout: 15
    # 消息消费失败后延迟级别
    delay-level-when-next-consume: 0
    # 是否顺序消费
    orderly: false

# 应用配置
spring:
  application:
    name: rocketmq-example
    
# 日志配置
logging:
  level:
    com.aliyun.openservices: INFO
    org.apache.rocketmq: INFO
```

### 3. RocketMQ 配置类
```java
@Configuration
@EnableConfigurationProperties(RocketMQProperties.class)
@Slf4j
public class RocketMQConfig {
    
    @Value("${rocketmq.name-server}")
    private String nameServer;
    
    @Value("${rocketmq.access-key}")
    private String accessKey;
    
    @Value("${rocketmq.secret-key}")
    private String secretKey;
    
    /**
     * 创建普通消息生产者
     */
    @Bean(name = "producer", initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        // 设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, "3000");
        // 设置失败重试次数
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, "3");
        
        producer.setProperties(properties);
        return producer;
    }
    
    /**
     * 创建事务消息生产者
     */
    @Bean(name = "transactionProducer", initMethod = "start", destroyMethod = "shutdown")
    public TransactionProducerBean buildTransactionProducer() {
        TransactionProducerBean producer = new TransactionProducerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_TRANSACTION");
        // 事务消息检查间隔时间，单位毫秒
        properties.setProperty(PropertyKeyConst.CheckImmunityTimeInSeconds, "10");
        
        producer.setProperties(properties);
        return producer;
    }
    
    /**
     * 创建顺序消息生产者
     */
    @Bean(name = "orderProducer", initMethod = "start", destroyMethod = "shutdown")
    public OrderProducerBean buildOrderProducer() {
        OrderProducerBean orderProducer = new OrderProducerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_ORDER");
        
        orderProducer.setProperties(properties);
        return orderProducer;
    }
}
```

## 二、消息生产者完整示例

### 1. 消息实体类
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderMessage {
    private String orderId;
    private String userId;
    private BigDecimal amount;
    private Integer status;
    private Date createTime;
}
```

### 2. 生产者服务类
```java
@Service
@Slf4j
public class RocketMQProducerService {
    
    @Resource
    private ProducerBean producer;
    
    @Resource
    private TransactionProducerBean transactionProducer;
    
    @Resource
    private OrderProducerBean orderProducer;
    
    /**
     * 发送普通消息
     */
    public SendResult sendNormalMessage(String topic, String tag, OrderMessage orderMessage) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTag(tag);
        message.setKey(orderMessage.getOrderId());
        message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
        
        try {
            SendResult sendResult = producer.send(message);
            log.info("发送普通消息成功, msgId: {}, orderId: {}", 
                     sendResult.getMessageId(), orderMessage.getOrderId());
            return sendResult;
        } catch (Exception e) {
            log.error("发送普通消息失败", e);
            throw new RuntimeException("发送消息失败", e);
        }
    }
    
    /**
     * 发送延时消息
     */
    public SendResult sendDelayMessage(String topic, String tag, OrderMessage orderMessage, long delayTime) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTag(tag);
        message.setKey(orderMessage.getOrderId());
        message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
        // 设置消息延时时间，单位毫秒
        message.setStartDeliverTime(System.currentTimeMillis() + delayTime);
        
        try {
            SendResult sendResult = producer.send(message);
            log.info("发送延时消息成功, msgId: {}, orderId: {}, delayTime: {}ms", 
                     sendResult.getMessageId(), orderMessage.getOrderId(), delayTime);
            return sendResult;
        } catch (Exception e) {
            log.error("发送延时消息失败", e);
            throw new RuntimeException("发送延时消息失败", e);
        }
    }
    
    /**
     * 发送顺序消息
     */
    public SendResult sendOrderMessage(String topic, String tag, OrderMessage orderMessage) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTag(tag);
        message.setKey(orderMessage.getOrderId());
        message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
        
        // 分区键，相同分区键的消息会被顺序消费
        String shardingKey = orderMessage.getUserId();
        
        try {
            SendResult sendResult = orderProducer.send(message, shardingKey);
            log.info("发送顺序消息成功, msgId: {}, orderId: {}, shardingKey: {}", 
                     sendResult.getMessageId(), orderMessage.getOrderId(), shardingKey);
            return sendResult;
        } catch (Exception e) {
            log.error("发送顺序消息失败", e);
            throw new RuntimeException("发送顺序消息失败", e);
        }
    }
    
    /**
     * 批量发送消息
     */
    public void sendBatchMessage(String topic, String tag, List<OrderMessage> orderMessages) {
        List<Message> messages = new ArrayList<>();
        
        for (OrderMessage orderMessage : orderMessages) {
            Message message = new Message();
            message.setTopic(topic);
            message.setTag(tag);
            message.setKey(orderMessage.getOrderId());
            message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
            messages.add(message);
        }
        
        try {
            // 批量发送，每批最多1000条
            int batchSize = 1000;
            for (int i = 0; i < messages.size(); i += batchSize) {
                List<Message> batch = messages.subList(i, 
                    Math.min(i + batchSize, messages.size()));
                producer.send(batch);
                log.info("批量发送消息成功, 数量: {}", batch.size());
            }
        } catch (Exception e) {
            log.error("批量发送消息失败", e);
            throw new RuntimeException("批量发送消息失败", e);
        }
    }
    
    /**
     * 发送事务消息
     */
    public void sendTransactionMessage(String topic, String tag, OrderMessage orderMessage) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTag(tag);
        message.setKey(orderMessage.getOrderId());
        message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
        
        try {
            transactionProducer.send(message, new LocalTransactionExecutor() {
                @Override
                public TransactionStatus execute(Message msg, Object arg) {
                    try {
                        // 执行本地事务
                        boolean success = executeLocalTransaction(orderMessage);
                        return success ? TransactionStatus.CommitTransaction : 
                                       TransactionStatus.RollbackTransaction;
                    } catch (Exception e) {
                        log.error("执行本地事务异常", e);
                        return TransactionStatus.RollbackTransaction;
                    }
                }
            }, null);
            
            log.info("发送事务消息成功, orderId: {}", orderMessage.getOrderId());
        } catch (Exception e) {
            log.error("发送事务消息失败", e);
            throw new RuntimeException("发送事务消息失败", e);
        }
    }
    
    /**
     * 异步发送消息
     */
    public void sendAsyncMessage(String topic, String tag, OrderMessage orderMessage) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTag(tag);
        message.setKey(orderMessage.getOrderId());
        message.setBody(JSON.toJSONString(orderMessage).getBytes(StandardCharsets.UTF_8));
        
        producer.sendAsync(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("异步发送消息成功, msgId: {}, orderId: {}", 
                         sendResult.getMessageId(), orderMessage.getOrderId());
            }
            
            @Override
            public void onException(OnExceptionContext context) {
                log.error("异步发送消息失败, orderId: {}", orderMessage.getOrderId(), 
                         context.getException());
            }
        });
    }
    
    private boolean executeLocalTransaction(OrderMessage orderMessage) {
        // 模拟本地事务执行
        log.info("执行本地事务, orderId: {}", orderMessage.getOrderId());
        return true;
    }
}
```

## 三、消息消费者完整示例

### 1. 消费者监听器配置
```java
@Component
@Slf4j
public class RocketMQConsumerListener {
    
    /**
     * 普通消息消费者
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean normalConsumer() {
        ConsumerBean consumer = new ConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_NORMAL_CONSUMER");
        // 消费线程数
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "20");
        // 设置批量消费的条数
        properties.setProperty(PropertyKeyConst.ConsumeMessageBatchMaxSize, "10");
        
        consumer.setProperties(properties);
        
        // 订阅消息
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic("TOPIC_ORDER");
        subscription.setExpression("TagA || TagB");
        
        subscriptionTable.put(subscription, new MessageListener() {
            @Override
            public Action consume(Message message, ConsumeContext context) {
                try {
                    String body = new String(message.getBody(), StandardCharsets.UTF_8);
                    OrderMessage orderMessage = JSON.parseObject(body, OrderMessage.class);
                    
                    log.info("接收到消息: msgId: {}, orderId: {}, tag: {}", 
                             message.getMsgID(), orderMessage.getOrderId(), message.getTag());
                    
                    // 处理业务逻辑
                    processOrder(orderMessage);
                    
                    // 消费成功
                    return Action.CommitMessage;
                } catch (Exception e) {
                    log.error("消费消息失败", e);
                    // 消费失败，稍后重试
                    return Action.ReconsumeLater;
                }
            }
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        return consumer;
    }
    
    /**
     * 顺序消息消费者
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public OrderConsumerBean orderConsumer() {
        OrderConsumerBean consumer = new OrderConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_ORDER_CONSUMER");
        // 顺序消息消费失败进行重试前的等待时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SuspendTimeMillis, "100");
        // 消息消费失败时重试次数
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, "20");
        
        consumer.setProperties(properties);
        
        Map<Subscription, MessageOrderListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic("TOPIC_ORDER");
        subscription.setExpression("*");
        
        subscriptionTable.put(subscription, new MessageOrderListener() {
            @Override
            public OrderAction consume(Message message, ConsumeOrderContext context) {
                try {
                    String body = new String(message.getBody(), StandardCharsets.UTF_8);
                    OrderMessage orderMessage = JSON.parseObject(body, OrderMessage.class);
                    
                    log.info("顺序消费消息: msgId: {}, orderId: {}, shardingKey: {}", 
                             message.getMsgID(), orderMessage.getOrderId(), 
                             message.getShardingKey());
                    
                    // 处理业务逻辑
                    processOrderSequentially(orderMessage);
                    
                    return OrderAction.Success;
                } catch (Exception e) {
                    log.error("顺序消费消息失败", e);
                    // 暂停当前队列的消费，稍后重试
                    return OrderAction.Suspend;
                }
            }
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        return consumer;
    }
    
    /**
     * 批量消费消息
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public BatchConsumerBean batchConsumer() {
        BatchConsumerBean consumer = new BatchConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_BATCH_CONSUMER");
        // 批量消费最大消息数量
        properties.setProperty(PropertyKeyConst.BatchConsumeMaxAwaitDurationInSeconds, "10");
        properties.setProperty(PropertyKeyConst.ConsumeMessageBatchMaxSize, "100");
        
        consumer.setProperties(properties);
        
        Map<Subscription, BatchMessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic("TOPIC_BATCH");
        subscription.setExpression("*");
        
        subscriptionTable.put(subscription, new BatchMessageListener() {
            @Override
            public Action consume(List<Message> messages, ConsumeContext context) {
                log.info("批量消费消息数量: {}", messages.size());
                
                for (Message message : messages) {
                    try {
                        String body = new String(message.getBody(), StandardCharsets.UTF_8);
                        OrderMessage orderMessage = JSON.parseObject(body, OrderMessage.class);
                        processOrder(orderMessage);
                    } catch (Exception e) {
                        log.error("处理消息失败: {}", message.getMsgID(), e);
                    }
                }
                
                return Action.CommitMessage;
            }
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        return consumer;
    }
    
    private void processOrder(OrderMessage orderMessage) {
        // 模拟业务处理
        log.info("处理订单: {}", orderMessage);
    }
    
    private void processOrderSequentially(OrderMessage orderMessage) {
        // 模拟顺序处理
        log.info("顺序处理订单: {}", orderMessage);
    }
}
```

### 2. Spring RocketMQ 注解方式消费
```java
@Component
@Slf4j
public class RocketMQAnnotationConsumer {
    
    /**
     * 普通消息消费
     */
    @RocketMQMessageListener(
        topic = "TOPIC_SPRING_BOOT",
        consumerGroup = "GID_SPRING_CONSUMER",
        consumeMode = ConsumeMode.CONCURRENTLY,
        consumeThreadMax = 64,
        maxReconsumeTimes = 3
    )
    @Component
    public class NormalConsumer implements RocketMQListener<OrderMessage> {
        
        @Override
        public void onMessage(OrderMessage orderMessage) {
            log.info("Spring方式消费消息: {}", orderMessage);
            // 处理业务逻辑
            processOrder(orderMessage);
        }
    }
    
    /**
     * 顺序消息消费
     */
    @RocketMQMessageListener(
        topic = "TOPIC_SPRING_ORDER",
        consumerGroup = "GID_SPRING_ORDER_CONSUMER",
        consumeMode = ConsumeMode.ORDERLY,
        maxReconsumeTimes = 5
    )
    @Component
    public class OrderlyConsumer implements RocketMQListener<OrderMessage> {
        
        @Override
        public void onMessage(OrderMessage orderMessage) {
            log.info("Spring方式顺序消费消息: {}", orderMessage);
            // 顺序处理业务逻辑
            processOrderSequentially(orderMessage);
        }
    }
    
    /**
     * 带返回值的消费者（用于事务消息回查）
     */
    @RocketMQMessageListener(
        topic = "TOPIC_SPRING_REPLY",
        consumerGroup = "GID_SPRING_REPLY_CONSUMER",
        replyTimeout = 3000
    )
    @Component
    public class ReplyConsumer implements RocketMQReplyListener<OrderMessage, String> {
        
        @Override
        public String onMessage(OrderMessage orderMessage) {
            log.info("Spring方式消费消息并返回结果: {}", orderMessage);
            // 处理业务逻辑并返回结果
            return processOrderWithResult(orderMessage);
        }
    }
    
    private void processOrder(OrderMessage orderMessage) {
        // 业务处理逻辑
        log.info("处理订单: {}", orderMessage.getOrderId());
    }
    
    private void processOrderSequentially(OrderMessage orderMessage) {
        // 顺序处理逻辑
        log.info("顺序处理订单: {}", orderMessage.getOrderId());
    }
    
    private String processOrderWithResult(OrderMessage orderMessage) {
        // 处理并返回结果
        return "SUCCESS:" + orderMessage.getOrderId();
    }
}
```

## 四、高级功能示例

### 1. 消息过滤
```java
@Service
@Slf4j
public class MessageFilterService {
    
    /**
     * SQL92 过滤示例
     */
    public void consumeWithSqlFilter() {
        ConsumerBean consumer = new ConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_SQL_FILTER");
        
        consumer.setProperties(properties);
        
        // SQL92 过滤表达式
        String sqlExpression = "amount > 100 AND status IN (1, 2)";
        
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic("TOPIC_SQL_FILTER");
        subscription.setExpression(sqlExpression);
        subscription.setType("SQL92");
        
        subscriptionTable.put(subscription, (message, context) -> {
            log.info("SQL过滤后的消息: {}", new String(message.getBody()));
            return Action.CommitMessage;
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        consumer.start();
    }
    
    /**
     * Tag 过滤示例
     */
    public void consumeWithTagFilter() {
        ConsumerBean consumer = new ConsumerBean();
        
        Properties properties = new Properties();
        // ... 设置属性
        
        consumer.setProperties(properties);
        
        // Tag 过滤表达式
        String tagExpression = "TagA || TagB || TagC";
        
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic("TOPIC_TAG_FILTER");
        subscription.setExpression(tagExpression);
        
        subscriptionTable.put(subscription, (message, context) -> {
            log.info("Tag过滤后的消息, Tag: {}", message.getTag());
            return Action.CommitMessage;
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        consumer.start();
    }
}
```

### 2. 消息轨迹
```java
@Configuration
public class MessageTraceConfig {
    
    /**
     * 启用消息轨迹的生产者
     */
    @Bean
    public ProducerBean traceProducer() {
        ProducerBean producer = new ProducerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        // 启用消息轨迹
        properties.setProperty(PropertyKeyConst.MsgTraceSwitch, "true");
        
        producer.setProperties(properties);
        return producer;
    }
    
    /**
     * 启用消息轨迹的消费者
     */
    @Bean
    public ConsumerBean traceConsumer() {
        ConsumerBean consumer = new ConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_TRACE_CONSUMER");
        // 启用消息轨迹
        properties.setProperty(PropertyKeyConst.MsgTraceSwitch, "true");
        
        consumer.setProperties(properties);
        return consumer;
    }
}
```

### 3. 死信队列处理
```java
@Service
@Slf4j
public class DeadLetterQueueService {
    
    /**
     * 处理死信队列消息
     */
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean deadLetterConsumer() {
        ConsumerBean consumer = new ConsumerBean();
        
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, nameServer);
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_DLQ_CONSUMER");
        
        consumer.setProperties(properties);
        
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        // 死信队列主题名称格式: %DLQ%+ConsumerGroup
        subscription.setTopic("%DLQ%GID_NORMAL_CONSUMER");
        subscription.setExpression("*");
        
        subscriptionTable.put(subscription, new MessageListener() {
            @Override
            public Action consume(Message message, ConsumeContext context) {
                try {
                    log.warn("处理死信消息: msgId: {}, 原始Topic: {}", 
                            message.getMsgID(), message.getUserProperties("REAL_TOPIC"));
                    
                    // 记录到数据库或发送告警
                    handleDeadLetter(message);
                    
                    return Action.CommitMessage;
                } catch (Exception e) {
                    log.error("处理死信消息失败", e);
                    return Action.ReconsumeLater;
                }
            }
        });
        
        consumer.setSubscriptionTable(subscriptionTable);
        return consumer;
    }
    
    private void handleDeadLetter(Message message) {
        // 处理死信逻辑：记录日志、发送告警、人工介入等
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.error("死信消息内容: {}", body);
        // 可以保存到数据库供后续分析
    }
}
```

## 五、性能监控和管理

### 1. 自定义监控指标
```java
@Component
@Slf4j
public class RocketMQMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final AtomicLong sendSuccessCount = new AtomicLong(0);
    private final AtomicLong sendFailCount = new AtomicLong(0);
    private final AtomicLong consumeSuccessCount = new AtomicLong(0);
    private final AtomicLong consumeFailCount = new AtomicLong(0);
    
    public RocketMQMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initMetrics();
    }
    
    private void initMetrics() {
        // 发送成功计数
        Gauge.builder("rocketmq.send.success", sendSuccessCount, AtomicLong::get)
            .description("RocketMQ消息发送成功数")
            .register(meterRegistry);
            
        // 发送失败计数
        Gauge.builder("rocketmq.send.fail", sendFailCount, AtomicLong::get)
            .description("RocketMQ消息发送失败数")
            .register(meterRegistry);
            
        // 消费成功计数
        Gauge.builder("rocketmq.consume.success", consumeSuccessCount, AtomicLong::get)
            .description("RocketMQ消息消费成功数")
            .register(meterRegistry);
            
        // 消费失败计数
        Gauge.builder("rocketmq.consume.fail", consumeFailCount, AtomicLong::get)
            .description("RocketMQ消息消费失败数")
            .register(meterRegistry);
    }
    
    public void recordSendSuccess() {
        sendSuccessCount.incrementAndGet();
    }
    
    public void recordSendFail() {
        sendFailCount.incrementAndGet();
    }
    
    public void recordConsumeSuccess() {
        consumeSuccessCount.incrementAndGet();
    }
    
    public void recordConsumeFail() {
        consumeFailCount.incrementAndGet();
    }
    
    /**
     * 消息发送监控切面
     */
    @Aspect
    @Component
    public class MessageSendAspect {
        
        @Around("@annotation(com.example.annotation.MessageSendMonitor)")
        public Object monitorSend(ProceedingJoinPoint joinPoint) throws Throwable {
            long startTime = System.currentTimeMillis();
            try {
                Object result = joinPoint.proceed();
                recordSendSuccess();
                return result;
            } catch (Exception e) {
                recordSendFail();
                throw e;
            } finally {
                long duration = System.currentTimeMillis() - startTime;
                Timer.Sample sample = Timer.start(meterRegistry);
                sample.stop(Timer.builder("rocketmq.send.duration")
                    .description("消息发送耗时")
                    .tag("method", joinPoint.getSignature().getName())
                    .register(meterRegistry));
            }
        }
    }
}
```

### 2. 健康检查
```java
@Component
public class RocketMQHealthIndicator implements HealthIndicator {
    
    @Resource
    private ProducerBean producer;
    
    @Resource
    private ConsumerBean consumer;
    
    @Override
    public Health health() {
        try {
            // 检查生产者状态
            boolean producerHealthy = checkProducerHealth();
            
            // 检查消费者状态
            boolean consumerHealthy = checkConsumerHealth();
            
            if (producerHealthy && consumerHealthy) {
                return Health.up()
                    .withDetail("producer", "UP")
                    .withDetail("consumer", "UP")
                    .build();
            } else {
                return Health.down()
                    .withDetail("producer", producerHealthy ? "UP" : "DOWN")
                    .withDetail("consumer", consumerHealthy ? "UP" : "DOWN")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
    
    private boolean checkProducerHealth() {
        // 发送测试消息检查生产者健康状态
        try {
            Message message = new Message();
            message.setTopic("HEALTH_CHECK_TOPIC");
            message.setBody("health check".getBytes());
            producer.send(message);
            return true;
        } catch (Exception e) {
            log.error("生产者健康检查失败", e);
            return false;
        }
    }
    
    private boolean checkConsumerHealth() {
        // 检查消费者是否正常运行
        return consumer.isStarted();
    }
}
```

## 六、Controller 层示例

```java
@RestController
@RequestMapping("/api/mq")
@Slf4j
public class RocketMQController {
    
    @Autowired
    private RocketMQProducerService producerService;
    
    /**
     * 发送普通消息
     */
    @PostMapping("/send/normal")
    public Result<SendResult> sendNormalMessage(@RequestBody OrderMessage orderMessage) {
        try {
            SendResult result = producerService.sendNormalMessage(
                "TOPIC_ORDER", "TagA", orderMessage);
            return Result.success(result);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送延时消息
     */
    @PostMapping("/send/delay")
    public Result<SendResult> sendDelayMessage(
            @RequestBody OrderMessage orderMessage,
            @RequestParam("delaySeconds") int delaySeconds) {
        try {
            SendResult result = producerService.sendDelayMessage(
                "TOPIC_ORDER", "TagDelay", orderMessage, delaySeconds * 1000L);
            return Result.success(result);
        } catch (Exception e) {
            log.error("发送延时消息失败", e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送顺序消息
     */
    @PostMapping("/send/order")
    public Result<SendResult> sendOrderMessage(@RequestBody OrderMessage orderMessage) {
        try {
            SendResult result = producerService.sendOrderMessage(
                "TOPIC_ORDER", "TagOrder", orderMessage);
            return Result.success(result);
        } catch (Exception e) {
            log.error("发送顺序消息失败", e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量发送消息
     */
    @PostMapping("/send/batch")
    public Result<Void> sendBatchMessage(@RequestBody List<OrderMessage> orderMessages) {
        try {
            producerService.sendBatchMessage("TOPIC_BATCH", "TagBatch", orderMessages);
            return Result.success(null);
        } catch (Exception e) {
            log.error("批量发送消息失败", e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送事务消息
     */
    @PostMapping("/send/transaction")
    public Result<Void> sendTransactionMessage(@RequestBody OrderMessage orderMessage) {
        try {
            producerService.sendTransactionMessage(
                "TOPIC_TRANSACTION", "TagTx", orderMessage);
            return Result.success(null);
        } catch (Exception e) {
            log.error("发送事务消息失败", e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
}

/**
 * 统一返回结果
 */
@Data
public class Result<T> {
    private boolean success;
    private String message;
    private T data;
    
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setData(data);
        return result;
    }
    
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
}
```

## 七、启动和测试类

```java
@SpringBootApplication
@EnableAspectJAutoProxy
public class RocketMQApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(RocketMQApplication.class, args);
    }
    
    /**
     * 应用启动后的初始化
     */
    @Component
    @Slf4j
    public class ApplicationStartupRunner implements CommandLineRunner {
        
        @Override
        public void run(String... args) throws Exception {
            log.info("=== RocketMQ 应用启动成功 ===");
            log.info("NameServer: {}", nameServer);
            log.info("AccessKey: {}****", accessKey.substring(0, 4));
            
            // 可以在这里进行一些初始化操作
            // 如：创建Topic、检查配置等
        }
    }
}
```

这个完整的配置和示例包含了：

1. **完整的 Spring Boot 配置**
2. **各种消息类型的发送示例**（普通、延时、顺序、批量、事务、异步）
3. **多种消费模式**（普通消费、顺序消费、批量消费）
4. **高级功能**（SQL过滤、消息轨迹、死信队列）
5. **监控和健康检查**
6. **完整的 REST API 接口**

您可以根据实际需求选择使用相应的功能。记得替换配置中的 NameServer 地址、AccessKey 和 SecretKey 为您的实际值。