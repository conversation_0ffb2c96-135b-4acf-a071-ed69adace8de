# RocketMQ Docker 本地安装和配置完整指南

本指南提供在本地使用Docker安装和配置RocketMQ的完整步骤，并结合用户中心项目的简历批量解析功能进行实际演示。

## 目录
1. [Docker安装RocketMQ](#1-docker安装rocketmq)
2. [启动和验证](#2-启动和验证)
3. [项目配置](#3-项目配置)
4. [实际使用示例](#4-实际使用示例)
5. [测试和验证](#5-测试和验证)
6. [故障排除](#6-故障排除)

## 1. Docker安装RocketMQ

### 1.1 目录结构准备

```bash
# 创建RocketMQ相关目录
mkdir -p docker/rocketmq/{conf,data/{nameserver/{logs,store},broker/{logs,store},redis,mysql},init}
```

### 1.2 配置文件

已创建的配置文件：
- `docker/rocketmq/docker-compose.yml` - Docker Compose配置
- `docker/rocketmq/conf/broker.conf` - Broker配置文件
- `src/main/resources/application-local.yml` - 本地环境应用配置

### 1.3 启动服务

```bash
# 进入docker目录
cd docker/rocketmq

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f rocketmq-nameserver
docker-compose logs -f rocketmq-broker
docker-compose logs -f rocketmq-console
```

### 1.4 服务端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| NameServer | 9876 | RocketMQ命名服务 |
| Broker | 10909, 10911 | 消息代理服务 |
| Console | 8080 | 管理界面 |
| Redis | 6379 | 缓存服务 |
| MySQL | 3306 | 数据库服务 |

## 2. 启动和验证

### 2.1 验证服务启动

```bash
# 检查NameServer
docker exec -it rocketmq-nameserver sh mqadmin updateTopic -n localhost:9876 -t TestTopic

# 检查Broker
docker exec -it rocketmq-broker sh mqadmin clusterList -n localhost:9876

# 访问管理界面
# 浏览器打开: http://localhost:8080
```

### 2.2 创建必要的Topic

```bash
# 进入Broker容器
docker exec -it rocketmq-broker bash

# 创建简历解析Topic
sh mqadmin updateTopic -n localhost:9876 -t RESUME_PARSE_TOPIC -c DefaultCluster

# 创建文件上传Topic
sh mqadmin updateTopic -n localhost:9876 -t FILE_UPLOAD_TOPIC -c DefaultCluster

# 查看Topic列表
sh mqadmin topicList -n localhost:9876
```

## 3. 项目配置

### 3.1 应用配置更新

确保 `application-local.yml` 中的RocketMQ配置正确：

```yaml
rocketmq:
  name-server: localhost:9876
  producer:
    group: user-center-producer-group
  consumer:
    group: user-center-consumer-group
```

### 3.2 启动应用

```bash
# 使用本地配置启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 或者
java -jar target/user-center-*.jar --spring.profiles.active=local
```

## 4. 实际使用示例

### 4.1 消息生产者示例

基于现有的 `MessageProducerService`：

```java
@RestController
@RequestMapping("/test")
public class RocketMQTestController {
    
    @Autowired
    private MessageProducerService messageProducer;
    
    /**
     * 测试发送简历解析消息
     */
    @PostMapping("/send-resume-parse")
    public Result<String> sendResumeParseMessage() {
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId(UUID.randomUUID().toString());
        message.setBatchId("test_batch_" + System.currentTimeMillis());
        message.setFileName("test_resume.pdf");
        message.setFileUrl("test/path/resume.pdf");
        message.setUserId(1L);
        
        try {
            messageProducer.sendResumeParseMessage(message);
            return Result.success("消息发送成功: " + message.getMessageId());
        } catch (Exception e) {
            return Result.error("消息发送失败: " + e.getMessage());
        }
    }
}
```

### 4.2 消息消费者

现有的 `ResumeParseMessageConsumer` 会自动消费消息：

```java
@Component
@RocketMQMessageListener(
    topic = "RESUME_PARSE_TOPIC",
    consumerGroup = "resume-parse-consumer-group"
)
public class ResumeParseMessageConsumer implements RocketMQListener<ResumeParseMessage> {
    
    @Override
    public void onMessage(ResumeParseMessage message) {
        log.info("收到简历解析消息: {}", message.getMessageId());
        // 处理消息逻辑
    }
}
```

### 4.3 批量简历解析流程

```java
/**
 * 批量上传简历并解析
 */
@PostMapping("/batch-upload")
public Result<AsyncParseResultVO> batchUploadResumes(
    @RequestParam("files") MultipartFile[] files,
    @RequestParam(value = "userId", required = false) Long userId) {
    
    // 1. 生成批次ID
    String batchId = trackingService.generateBatchId();
    
    // 2. 初始化进度跟踪
    trackingService.initializeBatchProgress(batchId, files.length, "RESUME_PARSE");
    
    // 3. 创建上传请求
    List<FileUploadRequest> requests = Arrays.stream(files)
        .map(file -> new FileUploadRequest(
            file.getOriginalFilename(),
            file.getBytes(),
            userId))
        .collect(Collectors.toList());
    
    // 4. 异步处理
    enhancedResumeParseService.batchUploadAndParse(requests, batchId);
    
    return Result.success(new AsyncParseResultVO(batchId, "SUBMITTED"));
}
```

## 5. 测试和验证

### 5.1 连接测试

创建测试类验证RocketMQ连接：

```java
@SpringBootTest
@ActiveProfiles("local")
class RocketMQConnectionTest {
    
    @Autowired
    private MessageProducerService messageProducer;
    
    @Test
    void testConnection() {
        // 发送测试消息
        ResumeParseMessage testMessage = new ResumeParseMessage();
        testMessage.setMessageId("test-" + System.currentTimeMillis());
        testMessage.setFileName("test.pdf");
        
        assertDoesNotThrow(() -> {
            messageProducer.sendResumeParseMessage(testMessage);
        });
    }
}
```

### 5.2 Console界面监控

1. 访问 http://localhost:8080
2. 查看Topic列表
3. 监控消息生产和消费情况
4. 查看Consumer Group状态

### 5.3 API测试

```bash
# 测试发送消息
curl -X POST http://localhost:8080/test/send-resume-parse

# 测试批量上传（需要文件）
curl -X POST \
  -F "files=@test1.pdf" \
  -F "files=@test2.pdf" \
  -F "userId=1" \
  http://localhost:8080/api/resume/batch-upload
```

## 6. 故障排除

### 6.1 常见问题

1. **Broker连接失败**
   ```bash
   # 检查Broker配置
   docker logs rocketmq-broker
   
   # 确认IP配置
   # 在broker.conf中设置: brokerIP1=127.0.0.1
   ```

2. **消息发送失败**
   ```bash
   # 检查Topic是否存在
   docker exec -it rocketmq-broker sh mqadmin topicList -n localhost:9876
   
   # 检查Producer配置
   # 确认name-server地址正确
   ```

3. **消费者无法接收消息**
   ```bash
   # 检查Consumer Group
   docker exec -it rocketmq-broker sh mqadmin consumerProgress -n localhost:9876
   
   # 检查应用日志
   tail -f logs/application.log
   ```

### 6.2 性能调优

```yaml
# 在application-local.yml中调整
rocketmq:
  producer:
    send-message-timeout: 5000
    max-message-size: 8388608  # 8MB
  consumer:
    consume-thread-min: 10
    consume-thread-max: 30
```

### 6.3 清理和重启

```bash
# 停止所有服务
docker-compose down

# 清理数据（谨慎使用）
sudo rm -rf data/

# 重新创建目录并启动
mkdir -p data/{nameserver/{logs,store},broker/{logs,store}}
docker-compose up -d
```

## 7. 高级配置和优化

### 7.1 集群配置

如需配置RocketMQ集群，可以扩展docker-compose.yml：

```yaml
# 添加多个Broker实例
rocketmq-broker-slave:
  image: apache/rocketmq:5.1.4
  container_name: rocketmq-broker-slave
  ports:
    - "10912:10911"
  environment:
    NAMESRV_ADDR: "rocketmq-nameserver:9876"
  command: ["sh", "mqbroker", "-c", "/opt/rocketmq-5.1.4/conf/broker-slave.conf"]
```

### 7.2 监控集成

添加Prometheus监控：

```yaml
# 在docker-compose.yml中添加
prometheus:
  image: prom/prometheus:latest
  ports:
    - "9090:9090"
  volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
```

### 7.3 消息持久化策略

```bash
# 配置消息保留策略
# 在broker.conf中添加：
# fileReservedTime=168  # 7天
# deleteWhen=04         # 凌晨4点清理
```

## 8. 生产环境部署建议

### 8.1 安全配置

```yaml
# 启用ACL权限控制
rocketmq:
  producer:
    access-key: ${ROCKETMQ_ACCESS_KEY}
    secret-key: ${ROCKETMQ_SECRET_KEY}
  consumer:
    access-key: ${ROCKETMQ_ACCESS_KEY}
    secret-key: ${ROCKETMQ_SECRET_KEY}
```

### 8.2 性能优化

```yaml
# 生产环境推荐配置
rocketmq:
  producer:
    send-message-timeout: 3000
    retry-times-when-send-failed: 3
    max-message-size: 4194304
  consumer:
    consume-thread-min: 20
    consume-thread-max: 64
    consume-message-batch-max-size: 32
```

## 总结

通过以上配置，您已经成功在本地Docker环境中部署了RocketMQ，并与用户中心项目的简历批量解析功能集成。主要特点：

1. **完整的Docker环境**：包含RocketMQ、Redis、MySQL
2. **生产级配置**：支持持久化、监控、管理界面
3. **实际业务集成**：结合简历解析功能的完整示例
4. **错误处理**：包含重试、熔断、限流机制
5. **监控和测试**：提供完整的验证和测试方法
6. **扩展性**：支持集群部署和性能优化

现在您可以开始使用RocketMQ进行消息队列开发和测试了！

## 快速启动命令

```bash
# 1. 创建目录结构
mkdir -p docker/rocketmq/{conf,data/{nameserver/{logs,store},broker/{logs,store},redis,mysql}}

# 2. 启动服务
cd docker/rocketmq && docker-compose up -d

# 3. 创建Topic
docker exec -it rocketmq-broker sh mqadmin updateTopic -n localhost:9876 -t RESUME_PARSE_TOPIC -c DefaultCluster

# 4. 启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 5. 访问管理界面
open http://localhost:8080
```
